<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="arena" cnName="竞技场" priceType="arenaStamp" labelArr="all" type="arena">
		<type>
			<goods cnName="附身碎片" name="possession_heroChip_arena" defineLabel="possession_heroChip" price="200" dayBuyLimitNum="5"  buyLimitNum="80" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="百合花" name="lily_arena" defineLabel="lily" price="200" dayBuyLimitNum="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="烟花弩碎片" name="beadCrossbow_arena" defineLabel="beadCrossbow" price="500" chooseNumB="1" dayBuyLimitNum="50"><priceType>arenaStamp</priceType></goods>
			<goods cnName="中型银币堆" name="coinHeap_2_arena" defineLabel="coinHeap_2" price="500" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="大型银币堆" name="coinHeap_3_arena" defineLabel="coinHeap_3" price="2500" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			
			<goods cnName="强化石" name="strengthenStone_arena" defineLabel="strengthenStone" price="40" chooseNumB="1" dayBuyLimitNum="50"><priceType>arenaStamp</priceType></goods>
			<goods cnName="血石" name="bloodStone_arena" defineLabel="bloodStone" price="80" chooseNumB="1" dayBuyLimitNum="50"><priceType>arenaStamp</priceType></goods>
			<goods cnName="神能石" name="godStone_arena" defineLabel="godStone" price="10" chooseNumB="1" dayBuyLimitNum="200"><priceType>arenaStamp</priceType></goods>
			<goods cnName="转化石" name="converStone_arena" defineLabel="converStone" price="10" chooseNumB="1" dayBuyLimitNum="100"><priceType>arenaStamp</priceType></goods>
			<goods cnName="超能石" name="skillStone_arena" defineLabel="skillStone" price="4" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			
			<goods cnName="终级装备守护卡" name="ultiEquipEchelonCard_arena" defineLabel="ultiEquipEchelonCard" price="500" chooseNumB="1" dayBuyLimitNum="10"><priceType>arenaStamp</priceType></goods>
			<goods cnName="终级武器守护卡" name="ultiArmsEchelonCard_arena" defineLabel="ultiArmsEchelonCard" price="500" chooseNumB="1" dayBuyLimitNum="10"><priceType>arenaStamp</priceType></goods>
			
			<goods cnName="高级武器守护卡" name="highArmsEchelonCard_arena" defineLabel="highArmsEchelonCard" price="166" dayBuyLimitNum="30" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="高级装备守护卡" name="highEquipEchelonCard_arena" defineLabel="highEquipEchelonCard" price="166" dayBuyLimitNum="30" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="装备守护卡" name="equipEchelonCard_arena" defineLabel="equipEchelonCard" price="50" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="武器守护卡" name="armsEchelonCard_arena" defineLabel="armsEchelonCard" price="50" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			
			
			<![CDATA[
			<goods cnName="69级零件箱" name="partsChest69_arena" defineLabel="partsChest69"  price="10000" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="远古宝箱" name="dragonChest_arena" defineLabel="dragonChest" price="3500" chooseNumB="1" dayBuyLimitNum="5"><priceType>arenaStamp</priceType></goods>
			<goods cnName="传奇宝箱" name="magicChest_arena" defineLabel="magicChest" price="3500" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			<goods cnName="稀有宝箱" name="normalChest_arena" defineLabel="normalChest" price="3000" chooseNumB="1" otherArr="hot"><priceType>arenaStamp</priceType></goods>
			
			]]>
			
			<goods cnName="镭晶" name="armsRadium_arena" defineLabel="armsRadium" price="30" chooseNumB="1" dayBuyLimitNum="20"><priceType>arenaStamp</priceType></goods>
			<goods cnName="钛晶" name="armsTitanium_arena" defineLabel="armsTitanium" price="40" chooseNumB="1" dayBuyLimitNum="20"><priceType>arenaStamp</priceType></goods>
			<goods cnName="光能石" name="lightStone_arena" defineLabel="lightStone" price="150" chooseNumB="1" dayBuyLimitNum="10"><priceType>arenaStamp</priceType></goods>
			<goods cnName="技能刷新卡" name="skillFleshCard_arena" defineLabel="skillFleshCard" price="250" chooseNumB="1" dayBuyLimitNum="5"><priceType>arenaStamp</priceType></goods>
			
			
			<goods dataType="vehicle" inBagType="equip" cnName="挖掘者" name="Diggers_arena" defineLabel="Diggers" price="33000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="gene" inBagType="gene" cnName="嗜血尸狼基因体" name="PetZombieWolf_arena" defineLabel="PetZombieWolf" price="16000"><priceType>arenaStamp</priceType></goods>
			
			<goods cnName="变异催化剂" name="variationCatalyst_arena" defineLabel="variationCatalyst" price="40" chooseNumB="1"><priceType>arenaStamp</priceType></goods>
			
			<goods dataType="equip" name="exilesSuit_head" price="7000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="exilesSuit_coat" price="10000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="exilesSuit_pants" price="10000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="exilesSuit_belt" price="7000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="scavengersSuit_head" price="7000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="scavengersSuit_coat" price="10000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="equip" name="scavengersSuit_pants" price="10000"><priceType>arenaStamp</priceType></goods>
			<goods  dataType="equip" name="scavengersSuit_belt" price="7000"><priceType>arenaStamp</priceType></goods>
			
			<goods dataType="arms" levelLimit="65" cnName="散华" name="pistolSange" price="13000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" levelLimit="65" cnName="雷电" name="shotgunLightning" price="13000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" levelLimit="65" cnName="轰锤" name="rocketHummer" price="13000"><priceType>arenaStamp</priceType></goods>
			
			<goods dataType="gene" inBagType="gene" cnName="铁魁基因体" name="PetIronChief_arena" defineLabel="PetIronChief" price="19000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="gene" inBagType="gene" cnName="爆骷基因体" name="PetBoomSkull_arena" defineLabel="PetBoomSkull" price="19000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="fashion" inBagType="equip" cnName="红魔时装" name="massEffect" price="20000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" cnName="赤龙" name="shotgunRed" price="10500"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" cnName="朱雀" name="pistolRed" price="11500"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" cnName="猎鹰" name="sniperBlue" price="9000"><priceType>arenaStamp</priceType></goods>
			<goods dataType="arms" cnName="沙暴" name="rifleYellow" price="8000"><priceType>arenaStamp</priceType></goods>
		</type>
		
	</father>	
	
</data>