<?xml version="1.0" encoding="utf-8" ?>
<data>
	<gather name="level" cnName="关卡">
		<father name="demon" cnName="修罗">
			<achieve name="demonTime_1" cnName="速通修罗V1" ><gift>things;demStone;20</gift>
				<condition type="valueCompare" pro="demonSingle" value="1"/><description>本周有[condition.value]张修罗地图（非决斗）的通关时间少于30秒。</description>
			</achieve>
			<achieve name="demonTime_2" cnName="速通修罗V2" ><gift>things;demStone;20</gift>
				<condition type="valueCompare" pro="demonSingle" value="3"/><description>本周有[condition.value]张修罗地图（非决斗）的通关时间少于30秒。</description>
			</achieve>
			<achieve name="demonTime_3" cnName="速通修罗V3" ><gift>things;demStone;25</gift>
				<condition type="valueCompare" pro="demonSingle" value="5"/><description>本周有[condition.value]张修罗地图（非决斗）的通关时间少于30秒。</description>
			</achieve>
			<achieve name="demonTime_4" cnName="速通修罗V4" ><gift>things;demStone;30</gift>
				<condition type="valueCompare" pro="demonSingle8" value="1"/><description>本周有[condition.value]张修罗八地图（非决斗）的通关时间少于30秒。</description>
			</achieve>
			<achieve name="demonTime_5" cnName="速通修罗V5" ><gift>things;demStone;35</gift>
				<condition type="valueCompare" pro="demonSingle8" value="3"/><description>本周有[condition.value]张修罗八地图（非决斗）的通关时间少于30秒。</description>
			</achieve>
		</father>
		<father name="endless" cnName="无尽">
			<achieve name="endless10" cnName="无尽10层" medelProArr="specialPartsDropPro" achieveDiff="40">
				<condition type="valueCompare" pro="endlessGrade99" value="10"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless20" cnName="无尽20层" medelProArr="specialPartsDropPro" achieveDiff="80">
				<condition type="valueCompare" pro="endlessGrade99" value="20"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless50" cnName="无尽50层" medelProArr="specialPartsDropPro" achieveDiff="120">
				<condition type="valueCompare" pro="endlessGrade99" value="50"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless80" cnName="无尽80层" medelProArr="specialPartsDropPro" achieveDiff="160">
				<condition type="valueCompare" pro="endlessGrade99" value="80"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless100" cnName="无尽100层" medelProArr="specialPartsDropPro" achieveDiff="200">
				<condition type="valueCompare" pro="endlessGrade99" value="100"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless110" cnName="无尽110层" medelProArr="specialPartsDropPro" achieveDiff="220">
				<condition type="valueCompare" pro="endlessGrade99" value="110"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless120" cnName="无尽120层" medelProArr="specialPartsDropPro" achieveDiff="240">
				<condition type="valueCompare" pro="endlessGrade99" value="120"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless130" cnName="无尽130层" medelProArr="specialPartsDropPro" achieveDiff="260">
				<condition type="valueCompare" pro="endlessGrade99" value="130"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless140" cnName="无尽140层" medelProArr="specialPartsDropPro" achieveDiff="280">
				<condition type="valueCompare" pro="endlessGrade99" value="140"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless150" cnName="无尽150层" medelProArr="specialPartsDropPro" achieveDiff="300">
				<condition type="valueCompare" pro="endlessGrade99" value="150"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless200" cnName="无尽200层" medelProArr="specialPartsDropPro" achieveDiff="320">
				<condition type="valueCompare" pro="endlessGrade99" value="200"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
			<achieve name="endless300" cnName="无尽300层" medelProArr="specialPartsDropPro" achieveDiff="350">
				<condition type="valueCompare" pro="endlessGrade99" value="300"/><description>地图“实验室顶层”的无尽模式到达第[condition.value]层。</description>
			</achieve>
		</father>
		<father name="wilderKing" cnName="秘境">
			<achieve name="VirtualScorpion1" cnName="虚晶蝎x13" medelProArr="" achieveDiff="20"><gift>equip;sprintSword_1;1</gift>
				<condition type="valueCompare" pro="VirtualScorpion" value="13"/><description>击败秘境“虚晶蝎”[condition.value]次。</description>
			</achieve>
			<achieve name="SaberTiger1" cnName="异齿虎x13" medelProArr="" achieveDiff="20"><gift>equip;saberDarts_1;1</gift>
				<condition type="valueCompare" pro="SaberTiger" value="13"/><description>击败秘境“异齿虎”[condition.value]次。</description>
			</achieve>
			
			<achieve name="wilderKing_1" cnName="称霸秘境" medelProArr="blackEquipDropPro" achieveDiff="30">
				<condition type="valueCompare" pro="wilderKing" value="1"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_2" cnName="称霸秘境II" medelProArr="blackEquipDropPro" achieveDiff="60">
				<condition type="valueCompare" pro="wilderKing" value="5"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_3" cnName="称霸秘境III" medelProArr="blackEquipDropPro" achieveDiff="90">
				<condition type="valueCompare" pro="wilderKing" value="13"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_4" cnName="称霸秘境IV" medelProArr="blackEquipDropPro" achieveDiff="120">
				<condition type="valueCompare" pro="wilderKing" value="20"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_5" cnName="称霸秘境V" medelProArr="blackEquipDropPro" achieveDiff="150">
				<condition type="valueCompare" pro="wilderKing" value="28"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_6" cnName="称霸秘境VI" medelProArr="blackEquipDropPro" achieveDiff="170">
				<condition type="valueCompare" pro="wilderKing" value="34"/><description>击败[condition.value]个地狱难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_7" cnName="称霸秘境VII" medelProArr="blackEquipDropPro" achieveDiff="190">
				<condition type="valueCompare" pro="wilderKing7" value="5"/><description>击败[condition.value]个修罗难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_8" cnName="称霸秘境VIII" medelProArr="blackEquipDropPro" achieveDiff="210">
				<condition type="valueCompare" pro="wilderKing7" value="12"/><description>击败[condition.value]个修罗难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_9" cnName="称霸秘境IX" medelProArr="blackEquipDropPro" achieveDiff="230">
				<condition type="valueCompare" pro="wilderKing7" value="20"/><description>击败[condition.value]个修罗难度的秘境首领。</description>
			</achieve>
			<achieve name="wilderKing_10" cnName="称霸秘境X" medelProArr="blackEquipDropPro" achieveDiff="250">
				<condition type="valueCompare" pro="wilderKing7" value="30"/><description>击败[condition.value]个修罗难度的秘境首领。</description>
			</achieve>
		</father>
		
		
		<father name="weaponKillNum" cnName="副手消灭">
			<achieve name="weaponKillNum_1" cnName="怒不可遏" medelProArr="bulletDedut" achieveDiff="10">
				<condition type="valueCompare" pro="weaponKillNum" value="400"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_2" cnName="怒发冲冠" medelProArr="bulletDedut" achieveDiff="30">
				<condition type="valueCompare" pro="weaponKillNum" value="1500"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_3" cnName="怒气冲天" medelProArr="bulletDedut" achieveDiff="50">
				<condition type="valueCompare" pro="weaponKillNum" value="3000"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_4" cnName="恼羞成怒" medelProArr="bulletDedut" achieveDiff="70">
				<condition type="valueCompare" pro="weaponKillNum" value="5000"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_5" cnName="勃然大怒" medelProArr="bulletDedut" achieveDiff="80">
				<condition type="valueCompare" pro="weaponKillNum" value="10000"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_6" cnName="雷霆之怒" medelProArr="bulletDedut" achieveDiff="90">
				<condition type="valueCompare" pro="weaponKillNum" value="20000"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="weaponKillNum_7" cnName="万目睚眦" medelProArr="bulletDedut" achieveDiff="100">
				<condition type="valueCompare" pro="weaponKillNum" value="30000"/>
				<description>在普通关卡中，使用副手消灭的敌人累计[condition.value]个。</description>
			</achieve>
		</father>
		<father name="vehicleKillNum" cnName="载具消灭">
			<achieve name="vehicleKillNum_1" cnName="追风逐日" medelProArr="lottery" achieveDiff="10">
				<condition type="valueCompare" pro="vehicleKillNum" value="1000"/>
				<description>在普通关卡中，使用载具消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="vehicleKillNum_2" cnName="风驰电掣" medelProArr="lottery" achieveDiff="30">
				<condition type="valueCompare" pro="vehicleKillNum" value="5000"/>
				<description>在普通关卡中，使用载具消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="vehicleKillNum_3" cnName="疾如雷电" medelProArr="lottery" achieveDiff="50">
				<condition type="valueCompare" pro="vehicleKillNum" value="10000"/>
				<description>在普通关卡中，使用载具消灭的敌人累计[condition.value]个。</description>
			</achieve>
			<achieve name="vehicleKillNum_4" cnName="望尘莫及" medelProArr="lottery" achieveDiff="70">
				<condition type="valueCompare" pro="vehicleKillNum" value="20000"/>
				<description>在普通关卡中，使用载具消灭的敌人累计[condition.value]个。</description>
			</achieve>
		</father>
		
		
		<father name="killNum" cnName="屠尸">
			<achieve name="killNum_1" cnName="屠尸小兵" medelProArr="dpsMul" achieveDiff="5">
				<condition type="valueCompare" pro="allKillNum" value="500"/>
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
			<achieve name="killNum_2" cnName="屠尸勇士" medelProArr="dpsMul" achieveDiff="10">
				<condition type="valueCompare" pro="allKillNum" value="1000" />
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
			<achieve name="killNum_3" cnName="屠尸战士" medelProArr="dpsMul" achieveDiff="20">
				<condition type="valueCompare" pro="allKillNum" value="5000" />
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
			<achieve name="killNum_4" cnName="屠尸将军" medelProArr="dpsMul" achieveDiff="50">
				<condition type="valueCompare" pro="allKillNum" value="20000" />
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
			<achieve name="killNum_5" cnName="屠尸王" medelProArr="dpsMul" achieveDiff="100">
				<condition type="valueCompare" pro="allKillNum" value="100000" />
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
			<achieve name="killNum_6" cnName="屠尸大帝" medelProArr="dpsMul" achieveDiff="120">
				<condition type="valueCompare" pro="allKillNum" value="300000" />
				<description>累计消灭[condition.value]个敌人。</description>
			</achieve>
		</father>
		<father name="rightHand" cnName="无敌右手" autoLowB="1">
			<achieve name="rightHand_40" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="5">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="40~40" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_45" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="15">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="45~45" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_50" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="25">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="50~50" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_55" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="35">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="55~55" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_60" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="45">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="60~60" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_65" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="55">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="65~65" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_70" cnName="无敌右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="65">
				<condition trigger="normalLevelWin" type="rightHand" diff="0~0" levelLv="70~70" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_75" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="75">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="75~75" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_80" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="80">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="80~80" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_85" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="85">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="85~85" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_90" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="90">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="90~90" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_95" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="95">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="95~95" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="rightHand_99" cnName="超级右手[condition.maxLevelLv]级" medelProArr="hurtMul_sniper" achieveDiff="100">
				<condition trigger="normalLevelWin" type="rightHand" diff="2~2" levelLv="99~99" />
				<description>不上场任何队友和尸宠，不使用主动技能、载具、副手，同时只用徒手攻击，通关1次[levelStr]关卡。</description>
			</achieve>
		</father>
		
		<father name="brokenGunners" cnName="破枪手" autoLowB="1">
			<achieve name="brokenGunners_30" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="5">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="30~30" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_35" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="15">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="35~35" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_40" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="25">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="40~40" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_45" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="40">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="45~45" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_50" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="55">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="50~50" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_55" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="70">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="55~55" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_60" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="85">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="60~60" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_65" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="100">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="65~65" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_70" cnName="破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="110">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="3~3" levelLv="70~70" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_75" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="120">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="75~75" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_80" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="130">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="80~80" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_85" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="140">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="85~85" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_90" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="150">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="90~90" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_95" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="160">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="95~95" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="brokenGunners_99" cnName="超级破枪手[condition.maxLevelLv]级" medelProArr="dpsMul_rifle" achieveDiff="170">
				<condition trigger="normalLevelWin" type="brokenGunners" diff="4~4" levelLv="99~99" />
				<description>全队使用不装零件的蓝色或蓝色以下枪支，通关1次[levelStr]关卡。</description>
			</achieve>
		</father>
		
		<father name="naked" cnName="裸装通关" autoLowB="1">
			<achieve name="naked_30" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="5">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="30~30"/>
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_35" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="15">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="35~35" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_40" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="30">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="40~40" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_45" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="45">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="45~45" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_50" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="60">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="50~50" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_55" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="70">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="55~55" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_60" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="80">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="60~60" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_65" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="90">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="65~65" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_70" cnName="裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="100">
				<condition trigger="normalLevelWin" type="naked" diff="3~3" levelLv="70~70" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_75" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="110">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="75~75" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_80" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="115">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="80~80" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_85" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="120">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="85~85" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_90" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="130">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="90~90" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_95" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="140">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="95~95" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
			<achieve name="naked_99" cnName="超级裸奔王[condition.maxLevelLv]级"  medelProArr="lifeRateMul" achieveDiff="150">
				<condition trigger="normalLevelWin" type="naked" diff="4~4" levelLv="99~99" />
				<description>全队不穿任何装备，通关1次[levelStr]关卡。</description>
			</achieve>
		</father>
		
		
		<father name="seconds15" cnName="快速击毙">
			<achieve name="seconds15_1" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="5">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="狂战尸" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_2" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="15">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="狂战射手" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_3" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="30">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="巨毒尸" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_4" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="45">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="霸王毒蛛" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_5" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="60">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="暴君" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_6" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="70">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="天鹰小美" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_7" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="80">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="钢铁僵尸王" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_8" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="90">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="飓风巫尸" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_9" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="100">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="2~2" enemy="无疆骑士" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_10" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="110">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="4~4" enemy="火炮尸狼" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_11" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="115">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="4~4" enemy="狂战狼" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_12" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="120">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="4~4" enemy="狂野收割者" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_13" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="125">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="5~5" enemy="毒魔" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_14" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="130">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="5~5" enemy="狂人机器" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_15" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="135">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="5~5" enemy="末日坦克" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			
			
			<achieve name="seconds15_16" cnName="速灭[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="140">
				<condition trigger="bossDie" type="liveTime" value="15" model="normal" diff="5~5" enemy="战神" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			
			<achieve name="seconds15_17" cnName="瞬秒[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="145">
				<condition trigger="bossDie" type="liveTime" value="5" model="normal" diff="6~6" enemy="末日坦克" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
			<achieve name="seconds15_18" cnName="瞬秒[condition.enemy]" medelProArr="dpsMul_rocket" achieveDiff="150">
				<condition trigger="bossDie" type="liveTime" value="5" model="normal" diff="6~6" enemy="战神" />
				<description>消灭[levelStr]，不使用首领召唤卡和魂卡的情况下，从它出现到被消灭，只需[condition.value]秒！</description>
			</achieve>
		</father>
		
		<father name="winNoHurtAll" cnName="绝缘体">
			<achieve name="winNoHurt" cnName="绝缘体" medelProArr="dodge" achieveDiff="20">
				<condition trigger="normalLevelWin" type="heroHurtLess" value="0" model="normal"/>
				<description>保证在不受到任何伤害的情况下，通关任意关卡。</description>
			</achieve>
			<achieve name="winNoHurtSuper" cnName="超级绝缘体" medelProArr="dodge" achieveDiff="50">
				<condition trigger="normalLevelWin" type="heroHurtLess" value="0" model="normal" string="sameLevel" />
				<description>保证在不受到任何伤害的情况下，通关与你同等级的关卡。</description>
			</achieve>
		</father>
		<father name="levelOther" cnName="其他">
			<achieve name="winLessLife" cnName="涉险过关" medelProArr="headMul" achieveDiff="20">
				<condition trigger="bossDie" type="heroLifeMulLess" mul="0.05" model="normal" infoUnit="%"/>
				<description>消灭与你同级的首领之后，你的生命值小于[condition.mul]！</description>
			</achieve>
		</father>
		
	</gather>
	<gather name="drop" cnName="掉落收集">
		<!--  -->
		<father name="dropAll" cnName="好人品">
			<achieve name="goodLuck_2" cnName="霞光好人品" medelProArr="dpsMul_pistol" achieveDiff="30">
				<condition trigger="dropOrange" type="dropOrange" value="2" />
				<description>消灭1位首领同时掉落[condition.value]个橙色装备或武器。</description>
			</achieve>
			<achieve name="goodLuck_3" cnName="中国好人品" medelProArr="dpsMul_pistol" achieveDiff="55">
				<condition trigger="dropOrange" type="dropOrange" value="3" />
				<description>消灭1位首领同时掉落[condition.value]个橙色装备或武器。</description>
			</achieve>
			
		</father>
		<father name="badDropAll" cnName="渣人品">
			<achieve name="lowLuck_100" cnName="霞光渣人品" medelProArr="hurtMul_rifle" achieveDiff="15">
				<condition trigger="openUI" type="valueCompare" pro="bossNoOrangeNum" value="10" />
				<description>连续消灭[condition.value]个首领都不掉落橙色装备或者武器。</description>
			</achieve>
			<achieve name="lowLuck_200" cnName="中国渣人品" medelProArr="hurtMul_rifle" achieveDiff="30">
				<condition trigger="openUI" type="valueCompare" pro="bossNoOrangeNum" value="20" />
				<description>连续消灭[condition.value]个首领都不掉落橙色装备或者武器。</description>
			</achieve>
		</father>
		
		<father name="suit" cnName="套装">
			<achieve name="oneSuit_50" cnName="豪华套装[condition.level]级" medelProArr="reload" achieveDiff="30">
				<condition type="oneSuit" string="orange" level="50"/>
				<description>集齐1套[condition.level]级橙色套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_55" cnName="红色套装[condition.level]级" medelProArr="reload" achieveDiff="45">
				<condition type="oneSuit" string="red" level="55"/>
				<description>集齐1套[condition.level]级红色套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_60" cnName="红色套装[condition.level]级" medelProArr="reload" achieveDiff="55">
				<condition type="oneSuit" string="red" level="60"/>
				<description>集齐1套[condition.level]级红色套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_65" cnName="红色套装[condition.level]级" medelProArr="reload" achieveDiff="65">
				<condition type="oneSuit" string="red" level="65"/>
				<description>集齐1套[condition.level]级红色套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_70" cnName="红色套装[condition.level]级" medelProArr="reload" achieveDiff="75">
				<condition type="oneSuit" string="red" level="70"/>
				<description>集齐1套[condition.level]级红色套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_75" cnName="红色稀有套装[condition.level]级" medelProArr="reload" achieveDiff="85">
				<condition type="oneSuit" string="red" level="75" string2="rare" />
				<description>集齐1套[condition.level]级红色稀有套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_80" cnName="红色稀有套装[condition.level]级" medelProArr="reload" achieveDiff="90">
				<condition type="oneSuit" string="red" level="80" string2="rare" />
				<description>集齐1套[condition.level]级红色稀有套装，并穿上它！</description>
			</achieve>
			<achieve name="redSuit_85" cnName="黑色稀有套装[condition.level]级" medelProArr="reload" achieveDiff="95">
				<condition type="oneSuit" string="black" level="85"/>
				<description>集齐1套[condition.level]级黑色稀有套装，并穿上它！</description>
			</achieve>
		</father>	
		<father name="equipDps" cnName="战斗神装">	
			<achieve name="dpsEquip_50" cnName="战斗神装[condition.level]级" medelProArr="chargerMul" achieveDiff="30">
				<condition type="dpsEquip" level="50" value="1" />
				<description>获得1件[condition.level]级拥有4个战斗力或伤害属性的装备，并穿上它！</description>
			</achieve>
			<achieve name="dpsEquip_55" cnName="战斗神装[condition.level]级" medelProArr="chargerMul" achieveDiff="45">
				<condition type="dpsEquip" level="55" value="1" />
				<description>获得1件[condition.level]级拥有4个战斗力或伤害属性的装备，并穿上它！</description>
			</achieve>
			<achieve name="dpsEquip_60" cnName="战斗神装[condition.level]级" medelProArr="chargerMul" achieveDiff="55">
				<condition type="dpsEquip" level="60" value="1" />
				<description>获得1件[condition.level]级拥有4个战斗力或伤害属性的装备，并穿上它！</description>
			</achieve>
			<achieve name="dpsEquip_65" cnName="战斗神装[condition.level]级" medelProArr="chargerMul" achieveDiff="65">
				<condition type="dpsEquip" level="65" value="1" />
				<description>获得1件[condition.level]级拥有4个战斗力或伤害属性的装备，并穿上它！</description>
			</achieve>
			<achieve name="dpsEquip_70" cnName="战斗神装[condition.level]级" medelProArr="chargerMul" achieveDiff="70">
				<condition type="dpsEquip" level="70" value="1" />
				<description>获得1件[condition.level]级拥有4个战斗力或伤害属性的装备，并穿上它！</description>
			</achieve>
		</father>
		<father name="blackEquip" cnName="黑色神装">
			<achieve name="blackEquip_1" cnName="黑色神装" medelProArr="chargerMul" achieveDiff="80">
				<condition type="valueCompare" pro="blackEquipNum" value="2" />
				<description>穿上至少[condition.value]件暗黑装备。</description>
			</achieve>
			<achieve name="blackEquip_2" cnName="黑色神装II" medelProArr="chargerMul" achieveDiff="90">
				<condition type="valueCompare" pro="blackEquipNum" value="3" />
				<description>穿上至少[condition.value]件暗黑装备。</description>
			</achieve>
			<achieve name="blackEquip_3" cnName="黑色神装III" medelProArr="chargerMul" achieveDiff="100">
				<condition type="valueCompare" pro="blackEquipNum" value="4" />
				<description>穿上至少[condition.value]件暗黑装备。</description>
			</achieve>
			<achieve name="blackEquip_4" cnName="黑色神装IV" medelProArr="chargerMul" achieveDiff="110">
				<condition type="valueCompare" pro="blackEquipEvo7Num" value="4" />
				<description>穿上至少[condition.value]件进化至“绝世”的暗黑装备。</description>
			</achieve>
		</father>
		<father name="armsDrop" cnName="武器收集">
			<achieve name="armsDropNum_1" cnName="武器收集者" medelProArr="hurtMul" achieveDiff="15">
				<condition type="valueCompare" pro="dropArmsNum" value="100" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
			<achieve name="armsDropNum_2" cnName="武器收集专家" medelProArr="hurtMul" achieveDiff="25">
				<condition type="valueCompare" pro="dropArmsNum" value="200" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
			<achieve name="armsDropNum_3" cnName="武器收集大师" medelProArr="hurtMul" achieveDiff="50">
				<condition type="valueCompare" pro="dropArmsNum" value="1000" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
			<achieve name="armsDropNum_4" cnName="武器收集宗师" medelProArr="hurtMul" achieveDiff="70">
				<condition type="valueCompare" pro="dropArmsNum" value="4000" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
			<achieve name="armsDropNum_5" cnName="武器收集王" medelProArr="hurtMul" achieveDiff="90">
				<condition type="valueCompare" pro="dropArmsNum" value="10000" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
			<achieve name="armsDropNum_6" cnName="武器收集大帝" medelProArr="hurtMul" achieveDiff="100">
				<condition type="valueCompare" pro="dropArmsNum" value="30000" />
				<description>消灭怪物累计获得超过[condition.value]把武器。</description>
			</achieve>
		</father>
		<father name="blackArms" cnName="黑武收集">
			<achieve name="blackArms_1" cnName="黑武收集专家" medelProArr="hurtMul" achieveDiff="100">
				<condition type="valueCompare" pro="blackArmsNum" value="3" />
				<description>至少获得[condition.value]把黑色武器。</description>
			</achieve>
			<achieve name="blackArms_2" cnName="黑武收集大师" medelProArr="hurtMul" achieveDiff="110">
				<condition type="valueCompare" pro="blackArmsNum" value="5" />
				<description>至少获得[condition.value]把黑色武器。</description>
			</achieve>
			<achieve name="blackArms_3" cnName="黑武收集宗师" medelProArr="hurtMul" achieveDiff="120">
				<condition type="valueCompare" pro="blackArmsNum" value="7" />
				<description>至少获得[condition.value]把黑色武器。</description>
			</achieve>
			<achieve name="blackArms_4" cnName="黑武收集王" medelProArr="hurtMul" achieveDiff="130">
				<condition type="valueCompare" pro="blackArmsNum" value="10" />
				<description>至少获得[condition.value]把黑色武器。</description>
			</achieve>
		</father>
		
		<father name="darkgoldArms" cnName="神武收集">
			<achieve name="darkgoldArms_1" cnName="金武收集专家" medelProArr="blackArmsDropPro" achieveDiff="100">
				<condition type="valueCompare" pro="darkgoldArmsNum" value="1" />
				<description>至少获得[condition.value]种暗金武器。</description>
			</achieve>
			<achieve name="darkgoldArms_2" cnName="金武收集大师" medelProArr="blackArmsDropPro" achieveDiff="200">
				<condition type="valueCompare" pro="darkgoldArmsNum" value="3" />
				<description>至少获得[condition.value]种暗金武器。</description>
			</achieve>
			<achieve name="darkgoldArms_3" cnName="金武收集宗师" medelProArr="blackArmsDropPro" achieveDiff="300">
				<condition type="valueCompare" pro="darkgoldArmsNum" value="6" />
				<description>至少获得[condition.value]种暗金武器。</description>
			</achieve>
			
			<achieve name="purglodArms_1" cnName="无双收集专家" medelProArr="blackArmsDropPro" achieveDiff="350">
				<condition type="valueCompare" pro="purgoldArmsNum" value="2" />
				<description>至少获得[condition.value]种无双武器。</description>
			</achieve>
			<achieve name="purglodArms_2" cnName="无双收集大师" medelProArr="blackArmsDropPro" achieveDiff="400">
				<condition type="valueCompare" pro="purgoldArmsNum" value="5" />
				<description>至少获得[condition.value]种无双武器。</description>
			</achieve>
			<achieve name="purglodArms_3" cnName="无双收集宗师" medelProArr="blackArmsDropPro" achieveDiff="450">
				<condition type="valueCompare" pro="purgoldArmsNum" value="8" />
				<description>至少获得[condition.value]种无双武器。</description>
			</achieve>
			<achieve name="purglodArms_4" cnName="无双收集王" medelProArr="blackArmsDropPro" achieveDiff="500">
				<condition type="valueCompare" pro="purgoldArmsNum" value="11" />
				<description>至少获得[condition.value]种无双武器。</description>
			</achieve>
		</father>
		
		<father name="eleArms" cnName="元素武器">
			<achieve name="eleArms_1" cnName="元素武器x1" medelProArr="moreDpsMul" achieveDiff="30">
				<condition type="valueCompare" pro="eleArms45Num" value="1" />
				<description>拥有[condition.value]把45%元素伤害的武器。</description>
			</achieve>
			<achieve name="eleArms_2" cnName="元素武器x3" medelProArr="moreDpsMul" achieveDiff="60">
				<condition type="valueCompare" pro="eleArms45Num" value="3" />
				<description>拥有[condition.value]把45%元素伤害的武器。</description>
			</achieve>
			<achieve name="eleArms_3" cnName="元素武器x6" medelProArr="moreDpsMul" achieveDiff="90">
				<condition type="valueCompare" pro="eleArms45Num" value="6" />
				<description>拥有[condition.value]把45%元素伤害的武器。</description>
			</achieve>
			<achieve name="eleArms_4" cnName="元素武器x10" medelProArr="moreDpsMul" achieveDiff="120">
				<condition type="valueCompare" pro="eleArms45Num" value="10" />
				<description>拥有[condition.value]把45%元素伤害的武器。</description>
			</achieve>
		</father>
		
		<father name="vehicle" cnName="载具收集">
			<achieve name="vehicle_1" cnName="载具收集x5" medelProArr="vehicleDefMul" achieveDiff="40">
				<condition type="valueCompare" pro="vehicleNum" value="5" />
				<description>拥有[condition.value]种载具。</description>
			</achieve>
			<achieve name="vehicle_2" cnName="载具收集x10" medelProArr="vehicleDefMul" achieveDiff="80">
				<condition type="valueCompare" pro="vehicleNum" value="10" />
				<description>拥有[condition.value]种载具。</description>
			</achieve>
			<achieve name="vehicle_3" cnName="载具收集x14" medelProArr="vehicleDefMul" achieveDiff="120">
				<condition type="valueCompare" pro="vehicleNum" value="14" />
				<description>拥有[condition.value]种载具。</description>
			</achieve>
			<achieve name="vehicle_4" cnName="载具收集x17" medelProArr="vehicleDefMul" achieveDiff="160">
				<condition type="valueCompare" pro="vehicleNum" value="17" />
				<description>拥有[condition.value]种载具。</description>
			</achieve>
			
		</father>
		<father name="vehicleHigh" cnName="三阶载具">
			<achieve name="vehicleHigh_1" cnName="三阶载具x3" medelProArr="vehicleDpsMul" achieveDiff="70">
				<condition type="valueCompare" pro="vehicle3" value="3" />
				<description>拥有[condition.value]种三阶载具。</description>
			</achieve>
			<achieve name="vehicleHigh_2" cnName="三阶载具x6" medelProArr="vehicleDpsMul" achieveDiff="140">
				<condition type="valueCompare" pro="vehicle3" value="6" />
				<description>拥有[condition.value]种三阶载具。</description>
			</achieve>
			<achieve name="vehicleHigh_3" cnName="三阶载具x9" medelProArr="vehicleDpsMul" achieveDiff="210">
				<condition type="valueCompare" pro="vehicle3" value="9" />
				<description>拥有[condition.value]种三阶载具。</description>
			</achieve>
			
		</father>
		<father name="weaponDevice" cnName="副手装置">
			<achieve name="weapon_1" cnName="副手x5" medelProArr="" achieveDiff="70">
				<condition type="valueCompare" pro="weaponNum" value="5" /><gift>base;wilderKey;5</gift>
				<description>拥有[condition.value]种副手。</description>
			</achieve>
			<achieve name="device_1" cnName="装置x7" medelProArr="" achieveDiff="70">
				<condition type="valueCompare" pro="deviceNum" value="7" /><gift>base;wilderKey;5</gift>
				<description>拥有[condition.value]种装置。</description>
			</achieve>
			<achieve name="device_2" cnName="装置x13" medelProArr="" achieveDiff="130">
				<condition type="valueCompare" pro="deviceNum" value="13" /><gift>base;wilderKey;10</gift>
				<description>拥有[condition.value]种装置。</description>
			</achieve>
		</father>
	</gather>
	<gather name="pet" cnName="尸宠">
		<father name="petType" cnName="尸宠收集">
			<achieve name="petType_3" cnName="尸宠x3" medelProArr="rareGeneDropPro" achieveDiff="30">
				<condition type="valueCompare" pro="petType" value="3" />
				<description>拥有[condition.value]种宠物。</description>
			</achieve>
			<achieve name="petType_6" cnName="尸宠x6" medelProArr="rareGeneDropPro" achieveDiff="60">
				<condition type="valueCompare" pro="petType" value="6" />
				<description>拥有[condition.value]种宠物。</description>
			</achieve>
			<achieve name="petType_10" cnName="尸宠x10" medelProArr="rareGeneDropPro" achieveDiff="100">
				<condition type="valueCompare" pro="petType" value="10" />
				<description>拥有[condition.value]种宠物。</description>
			</achieve>
		</father>
		<father name="petStren" cnName="尸宠强化">
			<achieve name="petStren_1" cnName="尸宠强化x99" medelProArr="petBookDropPro" achieveDiff="20">
				<condition type="valueCompare" pro="petStren" value="99" />
				<description>任意宠物的任意属性强化到[condition.value]级。</description>
			</achieve>
			<achieve name="petStren_2" cnName="尸宠强化x120" medelProArr="petBookDropPro" achieveDiff="40">
				<condition type="valueCompare" pro="petStren" value="120" />
				<description>任意宠物的任意属性强化到[condition.value]级。</description>
			</achieve>
			<achieve name="petStren_3" cnName="尸宠强化x140" medelProArr="petBookDropPro" achieveDiff="60">
				<condition type="valueCompare" pro="petStren" value="140" />
				<description>任意宠物的任意属性强化到[condition.value]级。</description>
			</achieve>
		</father>
	</gather>
	<gather name="skill" cnName="技能">
		<father name="skill13" cnName="终极技能">
			<achieve name="skill13_10" cnName="终极技能x10" medelProArr="gemDropPro" achieveDiff="53">
				<condition type="valueCompare" pro="skill13Num" value="10" />
				<description>所有角色一共拥有[condition.value]个13级人物技能。</description>
			</achieve>
			<achieve name="skill13_20" cnName="终极技能x20" medelProArr="gemDropPro" achieveDiff="100">
				<condition type="valueCompare" pro="skill13Num" value="20" />
				<description>所有角色一共拥有[condition.value]个13级人物技能。</description>
			</achieve>
			<achieve name="skill13_30" cnName="终极技能x30" medelProArr="gemDropPro" achieveDiff="120">
				<condition type="valueCompare" pro="skill13Num" value="30" />
				<description>所有角色一共拥有[condition.value]个13级人物技能。</description>
			</achieve>
			<achieve name="skill13_40" cnName="终极技能x40" medelProArr="gemDropPro" achieveDiff="140">
				<condition type="valueCompare" pro="skill13Num" value="40" />
				<description>所有角色一共拥有[condition.value]个13级人物技能。</description>
			</achieve>
			<achieve name="skill13_48" cnName="终极技能x48" medelProArr="gemDropPro" achieveDiff="160">
				<condition type="valueCompare" pro="skill13Num" value="48" />
				<description>所有角色一共拥有[condition.value]个13级人物技能。</description>
			</achieve>
			<achieve name="skill14_17" cnName="十四级技能x17" medelProArr="gemDropPro" achieveDiff="180">
				<condition type="valueCompare" pro="skill14" value="17" />
				<description>所有角色一共拥有[condition.value]个14级人物技能。</description>
			</achieve>
		</father>
		
		
		
		<father name="gliding" cnName="滑翔">
			<achieve name="gliding_1" cnName="飘飘欲仙" medelProArr="damageMul" achieveDiff="50">
				<condition type="valueCompare" pro="glidingTime" value="50"/>
				<description>累计在空中滑翔时间超过[condition.value]分钟。</description>
			</achieve>
			<achieve name="gliding_2" cnName="天神下凡" medelProArr="damageMul" achieveDiff="100">
				<condition type="valueCompare" pro="glidingTime" value="100"/>
				<description>累计在空中滑翔时间超过[condition.value]分钟。</description>
			</achieve>
		</father>
		<father name="skillHurt" cnName="近视与远视">
			<achieve name="hyperopiaFillHurt" cnName="望远镜">
				<condition type="mulCompare" pro="hyperopiaMaxMul" mul="1" />
				<description>完成1次[condition.mul]的远视加成</description>
			</achieve>
			<achieve name="myopiaFillHurt" cnName="显微镜">
				<condition type="mulCompare" pro="myopiaMaxMul" mul="1.1" />
				<description>完成1次[condition.mul]的近视加成</description>
			</achieve>
		</father>
		<father name="skillGiftNum" cnName="馈赠">
			<achieve name="skillGiftNum_1" cnName="小礼" medelProArr="cdMul" achieveDiff="15">
				<condition type="valueCompare" pro="skillGiftNum" value="30"/>
				<description>一个关卡之内，触发馈赠效果[condition.value]次以上。</description>
			</achieve>
			<achieve name="skillGiftNum_2" cnName="中礼" medelProArr="cdMul" achieveDiff="25">
				<condition type="valueCompare" pro="skillGiftNum" value="50"/>
				<description>一个关卡之内，触发馈赠效果[condition.value]次以上。</description>
			</achieve>
			<achieve name="skillGiftNum_3" cnName="大礼" medelProArr="cdMul" achieveDiff="35">
				<condition type="valueCompare" pro="skillGiftNum" value="80"/>
				<description>一个关卡之内，触发馈赠效果[condition.value]次以上。</description>
			</achieve>
			<achieve name="skillGiftNum_4" cnName="豪礼" medelProArr="cdMul" achieveDiff="45">
				<condition type="valueCompare" pro="skillGiftNum" value="120"/>
				<description>一个关卡之内，触发馈赠效果[condition.value]次以上。</description>
			</achieve>
		</father>
		<father name="charm" cnName="魅惑">
			<achieve name="charm_1" cnName="搔首弄姿" medelProArr="cdMul" achieveDiff="10">
				<condition type="valueCompare" pro="charmMaxNum" value="4"/>
				<description>一个关卡之内，最多同时存在[condition.value]个魅惑单位。</description>
			</achieve>
			<achieve name="charm_2" cnName="妖娆多姿" medelProArr="cdMul" achieveDiff="20">
				<condition type="valueCompare" pro="charmMaxNum" value="7" />
				<description>一个关卡之内，最多同时存在[condition.value]个魅惑单位。</description>
			</achieve>
			<achieve name="charm_3" cnName="风情万种" medelProArr="cdMul" achieveDiff="30">
				<condition type="valueCompare" pro="charmMaxNum" value="10" />
				<description>一个关卡之内，最多同时存在[condition.value]个魅惑单位。</description>
			</achieve>
			<achieve name="charm_4" cnName="千娇百媚" medelProArr="cdMul" achieveDiff="40">
				<condition type="valueCompare" pro="charmMaxNum" value="14" />
				<description>一个关卡之内，最多同时存在[condition.value]个魅惑单位。</description>
			</achieve>
			<achieve name="charm_5" cnName="艳绝人寰" medelProArr="cdMul" achieveDiff="50">
				<condition type="valueCompare" pro="charmMaxNum" value="18" />
				<description>一个关卡之内，最多同时存在[condition.value]个魅惑单位。</description>
			</achieve>
		</father>
		
		<father name="moreMissile" cnName="万弹归宗">
			<achieve name="moreMissile_1" cnName="十弹归宗" medelProArr="dpsMul_sniper" achieveDiff="10">
				<condition type="valueCompare" pro="moreMissileMaxNum" value="90" />
				<description>1个关卡之内，累计释放[condition.value]颗万弹归宗导弹。</description>
			</achieve>
			<achieve name="moreMissile_2" cnName="百弹归宗" medelProArr="dpsMul_sniper" achieveDiff="30">
				<condition type="valueCompare" pro="moreMissileMaxNum" value="300" />
				<description>1个关卡之内，累计释放[condition.value]颗万弹归宗导弹。</description>
			</achieve>
			<achieve name="moreMissile_3" cnName="千弹归宗" medelProArr="dpsMul_sniper" achieveDiff="60">
				<condition type="valueCompare" pro="moreMissileMaxNum" value="1000" />
				<description>1个关卡之内，累计释放[condition.value]颗万弹归宗导弹。</description>
			</achieve>
		</father>
		<father name="rolling" cnName="翻滚">
			<achieve name="rolling_1" cnName="翻滚100次" medelProArr="" achieveDiff="50">
				<condition type="valueCompare" pro="rollNum" value="100" /><gift>things;speedDrug;3</gift>
				<description>累计翻滚[condition.value]次。</description>
			</achieve>
		</father>
	</gather>
	
	<gather name="bossCard" cnName="魂卡">
		<father name="bossCardPK" cnName="魂卡PK">
			<achieve name="bossCardPK_1" cnName="击败魂卡x10" medelProArr="">
				<condition type="valueCompare" pro="bossCardPKNum" value="10" /><gift>things;bossCardStamp;4</gift>
				<description>累计击败[condition.value]张敌人魂卡。</description>
			</achieve>
			<achieve name="bossCardPK_2" cnName="击败魂卡x40" medelProArr="">
				<condition type="valueCompare" pro="bossCardPKNum" value="40" /><gift>things;bossCardStamp;4</gift>
				<description>累计击败[condition.value]张敌人魂卡。</description>
			</achieve>
			<achieve name="bossCardPK_3" cnName="击败魂卡x100" medelProArr="">
				<condition type="valueCompare" pro="bossCardPKNum" value="100" /><gift>things;bossCardStamp;8</gift>
				<description>累计击败[condition.value]张敌人魂卡。</description>
			</achieve>
		</father>
		<father name="bossCardStar" cnName="抽魂卡">
			<achieve name="bossCardStar_5" cnName="五星魂卡" medelProArr="">
				<condition type="valueCompare" pro="bossCard5" value="1" /><gift>things;bossCardStamp;4</gift>
				<description>拥有[condition.value]张抽到五星魂卡。</description>
			</achieve>
			<achieve name="bossCardStar_6" cnName="六星魂卡" medelProArr="">
				<condition type="valueCompare" pro="bossCard6" value="1" /><gift>things;bossCardStamp;4</gift>
				<description>拥有[condition.value]张抽到六星魂卡。</description>
			</achieve>
			<achieve name="bossCardStar_7" cnName="七星魂卡" medelProArr="">
				<condition type="valueCompare" pro="bossCard7" value="1" /><gift>things;bossCardStamp;8</gift>
				<description>拥有[condition.value]张抽到七星魂卡。</description>
			</achieve>
		</father>
	</gather>
	<gather name="task" cnName="任务">
		<father name="memoryTask" cnName="回忆任务">
			<achieve name="memoryTask_1" cnName="回忆任务x20" medelProArr="rareEquipDropPro" achieveDiff="50">
				<condition type="valueCompare"  pro="memoryTask" value="20" /><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个回忆任务。</description>
			</achieve>
			<achieve name="memoryTask_2" cnName="回忆任务x45"  medelProArr="rareEquipDropPro" achieveDiff="100">
				<condition type="valueCompare"  pro="memoryTask" value="45" /><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个回忆任务。</description>
			</achieve>
			<achieve name="memoryTask_3" cnName="回忆任务x70"  medelProArr="rareEquipDropPro" achieveDiff="150">
				<condition type="valueCompare"  pro="memoryTask" value="70"/><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个回忆任务。</description>
			</achieve>
			<achieve name="memoryTask_4" cnName="回忆任务x96" medelProArr="rareEquipDropPro" achieveDiff="200">
				<condition type="valueCompare"  pro="memoryTask" value="96"  /><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个回忆任务。</description>
			</achieve>
		</father>
		
		<father name="spreadTask" cnName="外传任务">
			<achieve name="spreadTask_1" cnName="外传任务x7">
				<condition type="valueCompare"  pro="spreadTask" value="7"  /><gift>base;wilderKey;6</gift>
				<description>完成[condition.value]个外传任务。</description>
			</achieve>
			<achieve name="spreadTask_2" cnName="外传任务x15">
				<condition type="valueCompare"  pro="spreadTask" value="15"  /><gift>base;wilderKey;6</gift>
				<description>完成[condition.value]个外传任务。</description>
			</achieve>
		</father>
		
		<father name="bwallTask" cnName="破壁任务">
			<achieve name="bwallTask_1" cnName="破壁任务x10" medelProArr="rareArmsDropPro" achieveDiff="50">
				<condition type="valueCompare"  pro="bwallTask" value="10" /><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个破壁任务。</description>
			</achieve>
			<achieve name="bwallTask_2" cnName="破壁任务x20" medelProArr="rareArmsDropPro" achieveDiff="100">
				<condition type="valueCompare"  pro="bwallTask" value="20" /><gift>base;wilderKey;3</gift>
				<description>完成[condition.value]个破壁任务。</description>
			</achieve>
		</father>
		
		<father name="deputyTask" cnName="支线任务">
			<achieve name="deputyTask_1" cnName="支线任务x10">
				<condition type="valueCompare"  pro="deputyTask" value="10"  /><gift>base;wilderKey;5</gift>
				<description>完成[condition.value]个支线任务。</description>
			</achieve>
			<achieve name="deputyTask_2" cnName="支线任务x20">
				<condition type="valueCompare"  pro="deputyTask" value="20"  /><gift>base;wilderKey;5</gift>
				<description>完成[condition.value]个支线任务。</description>
			</achieve>
		</father>
		
		<father name="star5Task" cnName="五星任务">
			<achieve name="star5Task_1" cnName="五星任务x1" medelProArr="moreLifeMul" achieveDiff="10">
				<condition type="valueCompare"  pro="star5TaskNum" value="1"  />
				<description>完成[condition.value]个五星任务。</description>
			</achieve>
			<achieve name="star5Task_2" cnName="五星任务x3" medelProArr="moreLifeMul" achieveDiff="20">
				<condition type="valueCompare"  pro="star5TaskNum" value="3"  />
				<description>完成[condition.value]个五星任务。</description>
			</achieve>
			<achieve name="star5Task_3" cnName="五星任务x5" medelProArr="moreLifeMul" achieveDiff="30">
				<condition type="valueCompare"  pro="star5TaskNum" value="5"  />
				<description>完成[condition.value]个五星任务。</description>
			</achieve>
		</father>
		
		<father name="kingTask" cnName="擒王">
			<achieve name="maxKingLevel_55" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="10">
				<condition type="valueCompare" pro="maxKingLevel" value="55" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_60" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="25">
				<condition type="valueCompare" pro="maxKingLevel" value="60" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_65" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="40">
				<condition type="valueCompare" pro="maxKingLevel" value="65" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_70" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="55">
				<condition type="valueCompare" pro="maxKingLevel" value="70" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_75" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="70">
				<condition type="valueCompare" pro="maxKingLevel" value="75" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_80" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="85">
				<condition type="valueCompare" pro="maxKingLevel" value="80" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_85" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="100">
				<condition type="valueCompare" pro="maxKingLevel" value="85" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_90" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="110">
				<condition type="valueCompare" pro="maxKingLevel" value="90" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_95" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="120">
				<condition type="valueCompare" pro="maxKingLevel" value="95" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_100" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="130">
				<condition type="valueCompare" pro="maxKingLevel" value="100" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_105" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="135">
				<condition type="valueCompare" pro="maxKingLevel" value="105" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
			<achieve name="maxKingLevel_110" cnName="擒王[condition.value]级" medelProArr="lifeMul" achieveDiff="140">
				<condition type="valueCompare" pro="maxKingLevel" value="110" />
				<description>在擒王任务中，消灭等级超过[condition.value]级的首领！</description>
			</achieve>
		</father>
		
		<father name="dayTask" cnName="每日任务">
			<achieve name="oldRocket3" cnName="不服老的火炮" medelProArr="moveMul" achieveDiff="30">
				<condition trigger="taskComplete" type="taskPan" string="oldRocket" diff="2~2" />
				<description>完成一次3星难度的“古老的火炮”。</description>
			</achieve>
			<achieve name="runAway3" cnName="劫后余生" medelProArr="moveMul" achieveDiff="40">
				<condition trigger="taskComplete" type="taskPan" string="runAway" diff="2~2" />
				<description>完成一次3星难度的“逃出生天”。</description>
			</achieve>
			<achieve name="coinChase3" cnName="银矿追逐者" medelProArr="moveMul" achieveDiff="70">
				<condition trigger="taskComplete" type="taskPan" string="coinChase" diff="2~2" />
				<description>完成一次3星难度的“银币追逐者”。</description>
			</achieve>
		</father>
		
	</gather>
	
	<gather name="motion" cnName="运动">
		<father name="fly" cnName="升空">
			<achieve name="fly_1" cnName="升空[condition.value]码" medelProArr="moveMul" achieveDiff="5">
				<condition type="valueCompare" pro="maxFlyHigh" value="180"/>
				<description>在关卡中，起跳或者被击飞，最终上升高度要超过[condition.value]码。</description>
			</achieve>
			<achieve name="fly_2" cnName="升空[condition.value]码" medelProArr="moveMul" achieveDiff="20">
				<condition type="valueCompare" pro="maxFlyHigh" value="500"/>
				<description>在关卡中，起跳或者被击飞，最终上升高度要超过[condition.value]码。</description>
			</achieve>
			<achieve name="fly_3" cnName="升空[condition.value]码" medelProArr="moveMul" achieveDiff="40">
				<condition type="valueCompare" pro="maxFlyHigh" value="1000"/>
				<description>在关卡中，起跳或者被击飞，最终上升高度要超过[condition.value]码。</description>
			</achieve>
			<achieve name="fly_4" cnName="升空[condition.value]码" medelProArr="moveMul" achieveDiff="70">
				<condition type="valueCompare" pro="maxFlyHigh" value="1800"/>
				<description>在关卡中，起跳或者被击飞，最终上升高度要超过[condition.value]码。</description>
			</achieve>
		</father>
		<father name="fall" cnName="坠落">
			<achieve name="drop_1" cnName="坠落[condition.value]码" medelProArr="moveMul" achieveDiff="5">
				<condition type="valueCompare" pro="maxFallHigh" value="300"/>
				<description>在一次坠落中，坠落高度超过[condition.value]码。</description>
			</achieve>
			<achieve name="drop_2" cnName="坠落[condition.value]码" medelProArr="moveMul" achieveDiff="15">
				<condition type="valueCompare" pro="maxFallHigh" value="700"/>
				<description>在一次坠落中，坠落高度超过[condition.value]码。</description>
			</achieve>
			<achieve name="drop_3" cnName="坠落[condition.value]码" medelProArr="moveMul" achieveDiff="35">
				<condition type="valueCompare" pro="maxFallHigh" value="1100"/>
				<description>在一次坠落中，坠落高度超过[condition.value]码。</description>
			</achieve>
			<achieve name="drop_4" cnName="坠落[condition.value]码" medelProArr="moveMul" achieveDiff="65">
				<condition type="valueCompare" pro="maxFallHigh" value="1440"/>
				<description>在一次坠落中，坠落高度超过[condition.value]码。</description>
			</achieve>
		</father>
		
	</gather>	
	<gather name="special" cnName="特别">
		<father name="dieNum" cnName="最脆">
			
			<achieve name="dieNum_1" cnName="霞光市最脆" medelProArr="headMul" achieveDiff="5">
				<condition type="valueCompare" pro="dieNum" value="50" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_2" cnName="全省最脆" medelProArr="headMul" achieveDiff="15">
				<condition type="valueCompare" pro="dieNum" value="100" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_3" cnName="全国最脆" medelProArr="headMul" achieveDiff="30">
				<condition type="valueCompare" pro="dieNum" value="200" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_4" cnName="全球最脆" medelProArr="headMul" achieveDiff="45">
				<condition type="valueCompare" pro="dieNum" value="500" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_5" cnName="太阳系最脆" medelProArr="headMul" achieveDiff="60">
				<condition type="valueCompare" pro="dieNum" value="1000" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_6" cnName="银河最脆" medelProArr="headMul" achieveDiff="80">
				<condition type="valueCompare" pro="dieNum" value="5000" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="dieNum_7" cnName="宇宙最脆" medelProArr="headMul" achieveDiff="90">
				<condition type="valueCompare" pro="dieNum" value="20000" />
				<description>殒命次数超过[condition.value]次。</description>
			</achieve>
		</father>
		
		<father name="gotoDie" cnName="自寻短见">
			<achieve name="dieBelow_1" cnName="弱不经风" medelProArr="fightDedut" achieveDiff="10">
				<condition trigger="heroDie" type="heroLevelLess" value="5" diff="0~0" />
				<description>命殒于低于人物等级[condition.value]级的怪物（简单难度）手上。</description>
			</achieve>
			<achieve name="dieBelow_2" cnName="生命垂危" medelProArr="fightDedut" achieveDiff="20">
				<condition trigger="heroDie" type="heroLevelLess" value="10" diff="0~0" />
				<description>命殒于低于人物等级[condition.value]级的怪物（简单难度）手上。</description>
			</achieve>
			<achieve name="dieBelow_3" cnName="无药可救" medelProArr="fightDedut" achieveDiff="35">
				<condition trigger="heroDie" type="heroLevelLess" value="15" diff="0~0" />
				<description>命殒于低于人物等级[condition.value]级的怪物（简单难度）手上。</description>
			</achieve>
		</father>
		
	</gather>
	<gather name="other" cnName="其他">
		<father name="weather" cnName="天气">
			<achieve name="rain3" cnName="倾盆大雨" medelProArr="">
				<condition trigger="addWeather" type="rain" value="3"/><gift>base;partsCoin;20</gift>
				<description>遭遇大雨天气。</description>
			</achieve>
			<achieve name="heat3" cnName="酷热难耐" medelProArr="">
				<condition trigger="addWeather" type="heat" value="3"/><gift>base;partsCoin;20</gift>
				<description>遭遇酷热天气。</description>
			</achieve>
		</father>
		<father name="unionBattle" cnName="军队争霸">
			<achieve name="unionBattle1" cnName="军队争霸V1" medelProArr="dpsMul_flamer" achieveDiff="60">
				<condition type="valueCompare" pro="unionBattleScore" value="500" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
			<achieve name="unionBattle2" cnName="军队争霸V2" medelProArr="dpsMul_flamer" achieveDiff="90">
				<condition type="valueCompare" pro="unionBattleScore" value="700" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
			<achieve name="unionBattle3" cnName="军队争霸V3" medelProArr="dpsMul_flamer" achieveDiff="120">
				<condition type="valueCompare" pro="unionBattleScore" value="800" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
			<achieve name="unionBattle4" cnName="军队争霸V4" medelProArr="dpsMul_flamer" achieveDiff="140">
				<condition type="valueCompare" pro="unionBattleScore" value="880" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
			<achieve name="unionBattle5" cnName="军队争霸V5" medelProArr="dpsMul_flamer" achieveDiff="150">
				<condition type="valueCompare" pro="unionBattleScore" value="920" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
			<achieve name="unionBattle6" cnName="军队争霸V6" medelProArr="dpsMul_flamer" achieveDiff="160">
				<condition type="valueCompare" pro="unionBattleScore" value="960" />
				<description>争霸积分到达[condition.value]分。</description>
			</achieve>
		</father>
		<father name="love" cnName="好感度">
			<achieve name="love_1" cnName="好人缘" medelProArr="dayLoveAdd" achieveDiff="35">
				<condition type="valueCompare" pro="loveMaxNum" value="1" />
				<description>[condition.value]个队友的好感度或者忠诚度到达24000。</description>
			</achieve>
			<achieve name="love_2" cnName="好人缘II" medelProArr="dayLoveAdd" achieveDiff="70">
				<condition type="valueCompare" pro="loveMaxNum" value="2" />
				<description>[condition.value]个队友的好感度或者忠诚度到达24000。</description>
			</achieve>
			<achieve name="love_3" cnName="好人缘III" medelProArr="dayLoveAdd" achieveDiff="105">
				<condition type="valueCompare" pro="loveMaxNum" value="3" />
				<description>[condition.value]个队友的好感度或者忠诚度到达24000。</description>
			</achieve>
			<achieve name="love_4" cnName="好人缘IV" medelProArr="dayLoveAdd" achieveDiff="140">
				<condition type="valueCompare" pro="loveMaxNum" value="4" />
				<description>[condition.value]个队友的好感度或者忠诚度到达24000。</description>
			</achieve>
		</father>
		
		<father name="blackMarket" cnName="神秘商人">
			<achieve name="blackMarketMaxNum_1" cnName="交易专家" medelProArr="critPro3" achieveDiff="10">
				<condition type="valueCompare" pro="blackMarketMaxNum" value="1" infoB="1" infoUnit="次" />
				<description>一天中跟神秘商人的交易次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="blackMarketMaxNum_2" cnName="交易大师" medelProArr="critPro3" achieveDiff="30">
				<condition type="valueCompare" pro="blackMarketMaxNum" value="2" infoB="1" infoUnit="次" />
				<description>一天中跟神秘商人的交易次数超过[condition.value]次。</description>
			</achieve>
			<achieve name="blackMarketMaxNum_3" cnName="交易王" medelProArr="critPro3" achieveDiff="50">
				<condition type="valueCompare" pro="blackMarketMaxNum" value="3" infoB="1" infoUnit="次" />
				<description>一天中跟神秘商人的交易次数超过[condition.value]次。</description>
			</achieve>
		</father>
		
		<father name="dailySign" cnName="签到">
			<achieve name="dailySign30" cnName="签到王" medelProArr="critPro3" achieveDiff="60">
				<condition type="valueCompare" pro="dailySignNum" value="20"/>
				<description>当月签到次数超过[condition.value]天。</description>
			</achieve>
		</father>
		<father name="lottery" cnName="银币抽奖">
			<achieve name="lotteryCoin_1" cnName="喜爱银币" medelProArr="coinMul" achieveDiff="15">
				<condition type="valueCompare" pro="lotteryCoin" value="50" infoB="1" infoUnit="次" />
				<description>在关卡抽奖中，累计抽中银币到达[condition.value]次。</description>
			</achieve>
			<achieve name="lotteryCoin_2" cnName="热爱银币" medelProArr="coinMul" achieveDiff="30">
				<condition type="valueCompare" pro="lotteryCoin" value="120" infoB="1" infoUnit="次" />
				<description>在关卡抽奖中，累计抽中银币到达[condition.value]次。</description>
			</achieve>
			<achieve name="lotteryCoin_3" cnName="酷爱银币" medelProArr="coinMul" achieveDiff="50">
				<condition type="valueCompare" pro="lotteryCoin" value="300" infoB="1" infoUnit="次" />
				<description>在关卡抽奖中，累计抽中银币到达[condition.value]次。</description>
			</achieve>
		</father>
		<father name="lotteryArms" cnName="物品抽奖">
			<achieve name="lotteryOrange_1" cnName="喜爱橙色" medelProArr="dpsMul_shotgun" achieveDiff="10">
				<condition type="valueCompare" pro="lotteryAllOrangeNum" value="10"/>
				<description>在关卡抽奖中，累计抽中神护碎片[condition.value]次以上。</description>
			</achieve>
			<achieve name="lotteryOrange_2" cnName="热爱橙色" medelProArr="dpsMul_shotgun" achieveDiff="25">
				<condition type="valueCompare" pro="lotteryAllOrangeNum" value="30"/>
				<description>在关卡抽奖中，累计抽中神护碎片[condition.value]次以上。</description>
			</achieve>
			<achieve name="lotteryOrange_3" cnName="酷爱橙色" medelProArr="dpsMul_shotgun" achieveDiff="40">
				<condition type="valueCompare" pro="lotteryAllOrangeNum" value="50"/>
				<description>在关卡抽奖中，累计抽中神护碎片[condition.value]次以上。</description>
			</achieve>
		</father>
		
	</gather>
</data>