# 商店货币类型修改指南

## 概述
将游戏中所有商品的货币类型统一改为银币 (coin)

## 当前货币类型
- `coin` - 银币 (目标货币)
- `money` - 黄金
- `score` - 积分  
- `anniCoin` - 纪念币
- `tenCoin` - 十年币
- `demBall` - 万能球
- `partsCoin` - 零件券
- `exploitCards` - 军队功勋牌
- `arenaStamp` - 优胜券
- `taxStamp` - 商券
- `zongzi` - 粽子
- `pumpkin` - 南瓜

## 需要修改的文件

### 1. 主要商品配置
**文件**: `binaryData\281_XMLOut_goodsClass.bin`

**修改内容**:
```xml
<!-- 将所有 priceType 属性改为 coin -->
<father name="normal" cnName="普通" priceType="coin" ...>

<!-- 将所有 <priceType> 标签内容改为 coin -->
<priceType>coin</priceType>
```

### 2. 纪念币商店
**文件**: `binaryData\320_XMLOut_anniCoinClass.bin`
```xml
<father name="anniCoin" cnName="纪念币" priceType="coin" ...>
```

### 3. 十年币商店  
**文件**: `binaryData\85_XMLOut_tenCoinClass.bin`
```xml
<father name="tenCoin" cnName="十年币" priceType="coin" ...>
```

### 4. 万能球商店
**文件**: `binaryData\264_XMLOut_demBallClass.bin`
```xml
<father name="demBall" cnName="万能球" priceType="coin" ...>
```

### 5. 零件券商店
**文件**: `binaryData\88_XMLOut_partsCoinClass.bin`
```xml
<father name="partsCoin" cnName="零件券" priceType="coin" ...>
```

### 6. 军队商店
**文件**: `binaryData\349_XMLOut_exploitCardsClass.bin`
```xml
<father name="exploitCards" cnName="军队商店" priceType="coin" ...>
```

### 7. 积分商店
**文件**: `binaryData\360_XMLOut_scoreGoodsClass.bin`
```xml
<father name="score" cnName="积分" priceType="coin" ...>
```

### 8. 活动商品
**文件**: `binaryData\8_XMLOut_activeGoodsClass.bin`
```xml
<father name="pumpkin" cnName="粽子" priceType="coin" ...>
```

## 修改步骤

### 方法一: 使用脚本自动修改
1. 运行提供的Python脚本: `python change_currency_to_coin.py`
2. 脚本会自动备份原文件并进行修改

### 方法二: 手动修改
1. **备份文件** - 复制所有要修改的文件作为备份
2. **批量替换** - 使用文本编辑器的批量替换功能:
   - 查找: `priceType="money"`  替换为: `priceType="coin"`
   - 查找: `priceType="score"`  替换为: `priceType="coin"`
   - 查找: `priceType="anniCoin"`  替换为: `priceType="coin"`
   - 查找: `priceType="tenCoin"`  替换为: `priceType="coin"`
   - 查找: `priceType="demBall"`  替换为: `priceType="coin"`
   - 查找: `priceType="partsCoin"`  替换为: `priceType="coin"`
   - 查找: `priceType="exploitCards"`  替换为: `priceType="coin"`
   - 查找: `<priceType>money</priceType>`  替换为: `<priceType>coin</priceType>`
   - 查找: `<priceType>score</priceType>`  替换为: `<priceType>coin</priceType>`
   - 等等...

## 注意事项

1. **备份重要** - 修改前务必备份原文件
2. **价格调整** - 改为银币后可能需要调整商品价格，因为不同货币的价值不同
3. **游戏平衡** - 统一货币后需要重新平衡游戏经济系统
4. **重新编译** - 修改XML后需要重新编译游戏才能生效

## 价格建议调整

由于原来的货币价值不同，建议按以下比例调整价格:
- 黄金商品: 价格 × 100 (因为黄金比银币贵)
- 纪念币商品: 价格 × 50
- 十年币商品: 价格 × 30  
- 万能球商品: 价格 × 20
- 零件券商品: 价格 × 10
- 其他特殊货币: 根据稀有度调整

## 恢复方法
如需恢复原设置，将备份文件重命名回原文件名即可。
