<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="system" cnName="系统">
		<body name="setFrame" cnName="设置帧数"  input="num"  string="30" localB="1" />
		<body name="setToLocalTime" cnName="本地时间开关"/>
		<body name="newDay" cnName="新的一天"/>
		<body name="newWeek" cnName="新的一周"/>
		<body name="newWeek6" cnName="新一周（周六刷新）"/>
		<body name="speedOnlineTime" cnName="在线时间加速"   input="num"  string="0"/>
		<body name="timerTest" cnName="时钟测试" localB="1"/>
		<body name="showFlashPlayerVer" cnName="显示Flash版本"/>
		<body name="setInitErrorB" cnName="设置错误输出开关"/>
	</father>
	<father name="player" cnName="玩家">
		<body name="noZuobi" cnName="解除作弊"/>
		<body name="isZuobi" cnName="设定为作弊"/>
		<body name="showZuobi" cnName="作弊原因"/>
		<body name="setHeroLv" cnName="设定人物等级"  input="num"  string="85"/>
		<body name="addHeroExp" cnName="增加人物经验"  input="num"  string="10000"/>
		<body name="addCoin" cnName="添加银币"  input="num"  string="100"/>
		<body name="addSore" cnName="添加积分"  input="num"  string="100"/>
		<body name="clearAllDoubleAdd" cnName="清除所有双倍时间"/>
		<body name="setLoveValue" cnName="设置好感度值"  input="num"  string="10000"/>
		<body name="initKeySetting" cnName="初始化按键设置"/>
		<body name="setMainXY" cnName="设置主角坐标" input="str"  string="600,100"/>
		<body name="openNewPartner" cnName="增加上场队友"/>
		<body name="setHeroDefine" cnName="设置主角定义" input="str"  string="小樱"/>
		<body name="toP1" cnName="设置为p1" input="str"  string="文杰表哥"/>
	</father>
	<father name="pay"  cnName="黄金">
		<body name="pay" cnName="充值" input="num"  string="1000" localB="1" />
		<body name="testYue" cnName="设置黄金" input="num"  string="10000" localB="1" />
		<body name="testChongZhi" cnName="设置黄金累计充值" input="num"  string="10000" localB="1" />
		<body name="showPayCount" cnName="显示购买记录"/>
	</father>
	<father name="save" cnName="存档">
		<body name="initPlayerSave" cnName="初始化存档" localB="1" />
		<body name="savePlayerSave" cnName="存档"/>
		<body name="getSaveData" cnName="复制存档"/>
		
		<body name="getSaveThin" cnName="复制存档-缩减"/>
		<body name="getSaveNoThin" cnName="复制存档-未缩减"/>
		<body name="get4399Save" cnName="复制4399存档"/>
		<body name="get4399SaveThin" cnName="复制4399存档-缩减"/>
		<body name="get4399SaveNoThin" cnName="复制4399存档-未缩减"/>
		
		<body name="supplePlayerSave" cnName="存档补充处理" localB="1" />
		<body name="seeSave" cnName="debug存档" localB="1" />
		
		<body name="setSuppleSummer" cnName="设置暑假修复状态" input="num"  string="0" inputTitle="【0】待修复 【1】修复完无卡bug 【2】修复完使用了卡bug 【3】已经返回扣除的物品"/>
		<body name="suppleSummer" cnName="修复暑假签到"/>
		<body name="recoverSummer" cnName="恢复暑假签到修复前状态"/>
		<body name="logout" cnName="退出登录" />
		<body name="autoSaveTime" cnName="设置自动存档时间间隔" input="num"  string="300"/>
		
		
		
	</father>
	<father name="things" cnName="物品">
		<body name="addAllThings" cnName="添加所有物品"  input="num"  string="1"/>
		<body name="addAllKey" cnName="添加所有钥匙"  input="num"  string="100"/>
		<body name="addPartsAll" cnName="添加所有零件" input="str"  string="72,10"  inputTitle="输入格式“等级,个数”"/>
		<body name="addSpecialParts" cnName="添加特殊零件" input="str"  string="10"  inputTitle="输入格式“个数”"/>
		<body name="clearBag" cnName="清除当前背包"/>
		<body name="clearHouse" cnName="清除当前仓库"/>
		<body name="setBagLock" cnName="设置背包解锁位置数"  input="num"  string="24"/>
		<body name="sortByTime" cnName="按照日期排序"/>
		<body name="delNoPositionThings" cnName="清除位置溢出的物品"/>
		<body name="getThingsStrByPrice" cnName="输出指定价值的物品" input="num"  string="50" inputTitle="输入物品价值"/>
	</father>
	<father name="arena"  cnName="竞技场">
		<body name="setArenaNum" cnName="设置竞技场次数" input="num" string="5" />
		<body name="setArenaScore" cnName="设置竞技场分数" input="num" string="1" />
		<body name="setArenaScore" cnName="设置竞技场分数" input="num" string="1" />
		<body name="addArenaGift" cnName="添加竞技场排名奖励" input="num" string="1" inputTitle="输入排名" />
		<body name="setArenaTimeMul" cnName="设置竞技场时间速度" input="num" string="1" inputTitle="输入时间倍数，推荐30" />
		<body name="toOldTopId" cnName="换成老排行榜id" />
		<body name="toNewTopId" cnName="换成新排行榜id" />
		<body name="getTopData" cnName="输出排行榜数据" input="str"  string=""  inputTitle="id,页码,每页数量"/>
	</father>
	<father name="union" cnName="军队">
		<body name="showUnionInfo" cnName="显示军队当前建筑数据" localB="1" />
		<body name="clearUnionData" cnName="清除军队数据" localB="1" />
		<body name="setUnionRankLevel" cnName="设置军衔等级"  input="num"  string="10"/>
		<body name="setCreateUnionMoney" cnName="设置创建军队所需黄金"   input="num"  string="200" localB="1" />
		<body name="setContribution" cnName="设置个人贡献"   input="num"  string="10000" localB="1" />
		<body name="doVatiables" cnName="执行军队变量"   input="num"  string="0" localB="1" tip="防作弊变量id：134，值为奇数则关闭防作弊，值为偶数开启防作弊。" />
		<body name="completeAllUnionTask" cnName="完成所有军队任务"/>
		<body name="showSuppliesNum" cnName="显示最大物资预估数量"/>
		<body name="doUnionTask" cnName="完成军队任务id"   input="num"  string="0" localB="1" />
		<body name="fastUpgradeUnionLv" cnName="快速升级军队" input="num"  string="0"/>
		<body name="setUnionTitle" cnName="设置军队" input="str"  string=""  localB="1" inputTitle="输入名称" />
		<![CDATA[<body name="showBattleBtn" cnName="显示争霸按钮" localB="1"/>]]>
		
		<body name="setNowMapMax" cnName="取消争霸贡献限制" />
		<body name="openUnionRole" cnName="开启职位任免权" />
		<body name="showLog" cnName="显示军队日志" input="str"  string="1,20"  localB="1" inputTitle="输入格式“页码,1页条数”"/>
	</father>
	<father name="level" cnName="关卡">
		<body name="unlockGoto" cnName="解锁地图到" input="str"  string="星谷之巅"/>
		<body name="unlockAllMap" cnName="解锁所有地图"/>
		<body name="winAllMap" cnName="通关所有地图"/>
		<body name="unlockAllDiff" cnName="解锁所有关卡难度"/>
		<body name="setDpsMul" cnName="设置英雄战斗力倍数"  input="num"  string="99999"/>
		<body name="setAnger" cnName="设置怒气值" input="num"  string="100"/>
		<body name="testEnemy" cnName="小怪出兵开关"/>
		<body name="winLevel" cnName="通关"/>
		<body name="testLevel" cnName="沃土镇设为测试状态"/>
		<body name="restart" cnName="重玩关卡"/>
		<body name="setEndlessLv" cnName="设置无尽模式层级" input="num"  string="1"/>
		<body name="setBossLifePer" cnName="设置所有敌人生命值%" input="num"  string="0"/>
		<body name="setWeLifePer" cnName="设置我方生命值%" input="num"  string="0"/>
		<body name="killAllPartner" cnName="杀死所有我方队友"/>
		<body name="stopAccOpen" cnName="防加速检测"/>
		<body name="setAllDemDiy" cnName="设置全部修罗地图模式" input="str"  string="partner"/>
		<body name="gotoLevelDefine" cnName="前往关卡定义" input="str"  string=""/>
		<body name="showPlayerSkillText" cnName="输出所有角色当前技能"/>
		<body name="lottery" cnName="当前关卡抽奖" input="num"  string="1" inputTitle="输入次数"/>
		<body name="openAllSweeping" cnName="开启所有难度扫荡"/>
		<body name="clearMainCharger" cnName="清空当前P1的弹夹和携弹量"/>
		<body name="moveBody" cnName="移动id到坐标" input="str"  string="p1,-200,0" inputTitle="单位id,x,y"/>
	</father>
	<father name="wilder" cnName="秘境战场">
		<body name="unlockAllWider" cnName="解锁所有秘境"/>
		<body name="setNowWiderNum" cnName="设置当前秘境已使用次数" input="num"  string="0"/>
		<body name="ulockAllBossEditLevel" cnName="解锁所有战场首领"/>
	</father>
	<father name="ai"  cnName="游戏AI">
		<body name="heroAI" cnName="主角AI开关"/>
		<body name="stopEnemy" cnName="敌方AI开关"/>
		<body name="stopWe" cnName="我方AI开关"/>
		<body name="parasiticBoss" cnName="寄生当前Boss"/>
		<body name="parasiticPet" cnName="寄生当前宠物"/>
		<body name="parasiticId" cnName="寄生id" input="str"  string="小樱"/>
		<body name="charmAndParasiticNear" cnName="寄生最近敌人"/>
		<body name="drawMapSniper" cnName="绘制蜘蛛网"/>
		<body name="showWeWarningRange" cnName="显示我方寻敌范围"/>
		<body name="aiTestMode" cnName="AI测试模式" input="str"  string="weHurt" tip="enemyHurt,weHurt,bulletHit,enemyFollow"/>
	</father>
	
	
	<father name="count" cnName="统计">
		<body name="showLevelCount" cnName="统计关卡数据"/>
		<body name="showPayTest" cnName="统计所需货币"/>
		<body name="sendCount" cnName="发送统计事件" />
		<body name="setArmsTestDiyData" cnName="设置武器测试参数" input="str"  string="0,10" tip="武器编号,武器数量"/>
		<body name="startDemonMapTest" cnName="开始修罗模式测试" input="str"  string="11,0" tip="难度,地图编号"/>
	</father>
	<father name="drop" cnName="掉落">
		<body name="dropTest" cnName="掉落测试" input="num"  string="100"  inputTitle="输入测试次数"/>
		<body name="closeDropTest" cnName="关闭掉落测试"/>
		<body name="showDropAdd" cnName="显示掉率加成"/>
	</father>
	<father name="equip" cnName="武器装备">
		<body name="showArmsCreator" cnName="显示武器制作"/>
		<body name="setStrengthenLv" cnName="设置强化等级" input="num"  string="20" tip="设置当前强化界面中的武器、装备的强化等级" />
		<body name="setEvoLv" cnName="设置进化等级" input="num"  string="1" tip="设置当前进化界面中的武器、装备的进化等级" />
		<body name="addArmsChip" cnName="添加所有武器碎片"  input="num" string="1" inputTitle="输入个数"/>
		<body name="addRareArms" cnName="添加所有稀有武器"/>
		<body name="addBlackArms" cnName="添加所有黑色武器"/>
		<body name="addEquipChip" cnName="添加所有装备碎片"  input="num"  string="1"  inputTitle="输入个数"/>
		<body name="addSuit" cnName="添加所有套装"/>
		<body name="addAllFashion" cnName="添加所有时装"/>
		<body name="addAllDevice" cnName="添加所有装置1级"/>
		<body name="addAllWeapon" cnName="添加所有副手1级"/>
		<body name="dealAllArms" cnName="修复主角武器基础伤害"/>
		<body name="sameToFirst" cnName="拉动背包所有武器"/>
		<body name="bagSameToFirst" cnName="设置背包所有随机武器"/>
		<body name="setFirstArmsGodSkill" cnName="设置第一把武器神技" input="str"  string="穿刺,绝灭" tip="技能中文名，用英文字母逗号隔开"/>
		<body name="addRedArms" cnName="添加红色随机武器" input="str"  string="laser1,99,diff_8,10" tip="armsName,lv,dropName,num"/>
		<body name="addRanBlackArms" cnName="添加黑色随机武器" input="str"  string="99,1" tip="等级,数量"/>
		<body name="testBlackArms" cnName="测试黑色随机武器" input="str"  string="60,99" tip="等级范围"/>
		<body name="addBlackArmsPro" cnName="增加黑色随机武器掉率"/>
	</father>
	
	
	
	
	<father name="pet" cnName="飞船宠物">
		<body name="addNowCraftExp" cnName="当前飞船+经验值" input="num"  string="10000" inputTitle="输入经验值"/>
		<body name="setNowCraftLv" cnName="当前飞船设置等级" input="num"  string="10" inputTitle="输入等级"/>
		<body name="clearAllPet" cnName="清除所有宠物"/>
		<body name="setPetLv" cnName="设置当前尸宠等级" input="num"  string="85"/>
		<body name="addNowGenePro" cnName="添加基因体掉落概率" tip="添加当前图鉴界面基因体掉落概率" input="num"  string="100" inputTitle="输入概率，例如：100，就是100%"/>
	</father>
	
	
	<father name="skill" cnName="技能">
		
		<body name="unlockSkill" cnName="解锁技能系统"/>
		<body name="clearNowSkill" cnName="删除当前界面技能"/>
		<body name="getAllEnemySkillStr" cnName="获得所有怪物技能说明"/>
		<body name="addProfi" cnName="添加熟练度" input="num"  string="1"  inputTitle="输入熟练度"/>
		<body name="setProfi" cnName="设置熟练度" input="num"  string="10000"  inputTitle="输入熟练度"/>
		<body name="setDayProfi" cnName="设置今日熟练度" input="num"  string="1"  inputTitle="输入熟练度"/>
		<body name="bossCardSkillConver" cnName="魂卡技能中文转代码" input="str"  string=""  inputTitle="输入技能中文名，用空格间隔"/>
		<body name="skillCnArrTo" cnName="工厂技能中文转数组" input="str"  string=""  inputTitle="输入技能中文名，用,间隔"/>
	</father>
	
	<father name="task" cnName="任务">
		<body name="unlockTask" cnName="解锁任务系统"/>
		<body name="unlockMainTask" cnName="解锁所有主线任务"/>
		<body name="nowTaskComplete" cnName="完成当前任务"/>
		<body name="nowTaskGet" cnName="接取当前任务"/>
		<body name="delMainTaskMore" cnName="删除某个任务后的所有主线" input="str"  string="Hospital5_plot"  inputTitle="输入任务英文名"/>
	</father>
	
	<father name="ui" cnName="UI界面">
		<body name="showApp" cnName="显示App"  input="str"  string="food" inputTitle="输入app名称"/>
		<body name="hideStat" cnName="隐藏/显示性能"/>
		<body name="hideUI" cnName="隐藏/显示所有UI"/>
		
		<body name="stopHandUp" cnName="显示防挂机窗口"/>
		<body name="showGuide" cnName="显示教程"/>
		<body name="showHurt" cnName="所有伤害显示开关"/>
		<body name="setEffectUrl" cnName="设定指定特效的Url" input="str"  string="highlighterBoom:" inputTitle="特效名:地址"/>
		<body name="setBulletEffectUrl" cnName="设定指定子弹图像的Url" input="str"  string="highlighterSun:" inputTitle="子弹名:地址"/>
		<body name="scaleScene" cnName="缩放视野" input="num"  string="100" />
		
	</father>
	<father name="achieve" cnName="魂卡成就">
		<body name="addBosscardByXml" cnName="添加魂卡XML" input="str"  string="" inputTitle="输入xml格式的魂卡"/>
		<body name="createBosscard" cnName="生成魂卡XML" input="str"  string="" inputTitle="举例“战斗僵尸 5”"/>
		<body name="reachieve" cnName="清空当前成就列表状态" />
		<body name="completeAllAchieve" cnName="完成所有成就"/>
	</father>
	<father name="post" cnName="职务">
		<body name="addPostExp" cnName="添加职务经验" input="num"  string="100"  inputTitle="输入职务经验"/>
		<body name="setPostLv" cnName="设置职务等级" input="num"  string="1"  inputTitle="输入职务等级"/>
	</father>
	<father name="head" cnName="称号">
		<body name="addNowHead" cnName="添加当前界面称号"/>
	</father>
	<father name="peak" cnName="巅峰">
		<body name="setPeakLv" cnName="设置巅峰等级" input="num"  string="1"/>
		<body name="setPeakExp" cnName="设置巅峰经验" input="num"  string="10000000"/>
	</father>
	<father name="other" cnName="其他">
		<body name="setQixi2019High" cnName="设置七夕升天高度" input="num"  string="1" inputTitle="输入高度，关卡内有效"/>
		<body name="setPartnerExploit" cnName="设置当前队友功勋值" input="num"  string="1" inputTitle="输入功勋值"/>
		<body name="setActive" cnName="设置活跃值" input="num"  string="1" inputTitle="输入活跃值，输入0则清除设置"/>
		<body name="setSweepingNum" cnName="设置扫荡次数" input="num"  string="5"/>
		<body name="testChipBattleGift" cnName="测试国庆大作战礼包"/>
		<body name="testBowlIndex" cnName="元宵跳一跳设定编号"  input="num"  string="200"/>
		<body name="testSeverTime" cnName="测试服务器时间" input="num"  string="50"/>
		<body name="testUplevelBug" cnName="循环测试升级bug"/>
		<body name="testUplevelBug2" cnName="循环测试升级bug-快速"/>
		<body name="showParts90" cnName="87级零件数量"/>
		<body name="addAllFood" cnName="添加所有食材" input="num"  string="10" inputTitle="输入数量"/>
		<body name="setFoodProfiAll" cnName="设置厨艺值" input="num"  string="10" inputTitle="输入数量"/>
		<body name="getTopData" cnName="获得排行榜数据" input="str"  string="849,1,20"  inputTitle="输入格式：id,pageNum,pageSize"/>
		<body name="testTop" cnName="测试排行榜" input="num"  string="27" inputTitle="输入积分"/>
		<body name="setSimTopScore" cnName="设置本地排行榜模拟分数" input="num"  string="10" inputTitle="输入分数"/>
		<body name="md5" cnName="md5" input="str"  string="" inputTitle="输入文本"/>
		<body name="base64ToJson" cnName="base64转Json" input="str"  string="" inputTitle="输入文本"/>
		<body name="jsonToBase64" cnName="Json转base64" input="str"  string="" inputTitle="输入文本"/>
		<body name="base64ToXml" cnName="base64ToXml" input="str"  string="" inputTitle="输入文本"/>
		<body name="decodeText32" cnName="decodeText32" input="str"  string="" inputTitle="输入文本"/>
	</father>
</data>