<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="loveSkill" cnName="忠诚度技能">
		<skill><!-- dps -->
			<name>dedicationLove</name>
			<cnName>奉献</cnName>
			<minTriggerT>0.5</minTriggerT>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHurt</condition>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMul</effectType>
			<mul>0.85</mul>
			<range>900</range>
			<duration>4</duration>
			<description>受伤时，提升附近[range]码内队友[1-mul]的防御力，持续[duration]秒。</description>
			<stateEffectImg partType="mouth" con="add">skillEffect/rune_blue_shield</stateEffectImg>
		</skill>
		
		<skill><!-- dps -->
			<name>resistPerLove2</name>
			<cnName>巨伤盾三</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>isMulHurt,specialNumLess,canUnderHurt</otherConditionArr>
			<conditionRange>3</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>noMulHurt</effectType>
			<description>关卡中抵挡3次百分比伤害。</description>
			<targetEffectImg partType="body">generalEffect/hurtDefence</targetEffectImg>
		</skill>
		
		<skill>
			<name>defenceAddLove</name><cnName>防御力提升20%</cnName><everNoClearB>1</everNoClearB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<mul>0.8</mul><effectType>underHurtMul</effectType>
			<description>战斗中永久提升[1-mul]的防御力。</description>
		</skill>
		<skill>
			<name>vehicleAddLove30</name><cnName>载具攻防</cnName><everNoClearB>1</everNoClearB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target>
			<addType>state</addType><duration>99999</duration>
			<mul>1.3</mul><secMul>1.3</secMul><effectType>defenceAndAttack</effectType><valueString>vehicle</valueString>
			<description>战斗中提升载具[mul-1]的防御力和攻击力。</description>
		</skill>
		
		<skill><!-- dps -->
			<name>resistPerLoveAdd2</name>
			<cnName>继续抵挡3次百分比伤害</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>isMulHurt,specialNumLess,canUnderHurt</otherConditionArr>
			<conditionRange>3</conditionRange><![CDATA[这里的数字必须为2，和上面那个技能相加后可抵挡4次]]>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>noMulHurt</effectType>
			<description>关卡中抵挡6次百分比伤害。</description>
			<targetEffectImg partType="body">generalEffect/hurtDefence</targetEffectImg>
		</skill>
		
		
		<skill><!-- dps -->
			<name>underInvincibleHurtLove</name>
			<cnName>减少无敌敌人伤害</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>targetIsInvincible</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurtAndMul</effectType>
			<mul>0.75</mul>
			<description>减少来自无敌敌人[1-mul]的伤害，包括百分比伤害。</description>
		</skill>
		
		<skill><!-- dps -->
			<name>nearAddLifeLove</name>
			<cnName>靠近回血</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target noMeB="1" noVehicleB="1">me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>lifeRateMul</effectType>
			<mul>2.5</mul>
			<duration>0.5</duration>
			<range>80</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="body" con="add">generalEffect/lifeReply</stateEffectImg>
			<description>队友靠近表哥时，生命回复速度将加快[mul-1]。</description>
		</skill>
		
		<skill><!-- dps -->
			<name>mainResistPerLove</name>
			<cnName>P1角色抵挡3次百分比伤害</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<otherConditionArr>isMulHurt,specialNumLess,canUnderHurt</otherConditionArr>
			<conditionRange>3</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>noMulHurt</effectType>
			<description>P1角色在关卡中可抵挡3次百分比伤害。</description>
			<valueString>Striker</valueString>
			<targetEffectImg partType="body">generalEffect/hurtDefence</targetEffectImg>
		</skill>
		
		
	</father>
</data>