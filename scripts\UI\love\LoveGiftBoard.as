package UI.love
{
   import UI.UIOrder;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.button.NormalBtn;
   import UI.base.drag.ItemsDragController;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._app.love.define.LoveTalkDefine;
   import dataAll.items.IO_ItemsData;
   import dataAll.must.PlayerMustCtrl;
   import dataAll.must.define.MustDefine;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class LoveGiftBoard extends LoveChildBoard
   {
      
      private var txt:TextField;
      
      private var itemsTag:Sprite;
      
      private var btnSp:MovieClip;
      
      private var addBtnSp:MovieClip;
      
      private var btn:NormalBtn = new NormalBtn();
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var mustItemsBox:ItemsGripBox = new ItemsGripBox();
      
      private var itemsConTxt:TextField;
      
      private var numTxt:TextField;
      
      private var itemsCon:MovieClip;
      
      public var loveBox:LoveBox;
      
      private var textString:String = "";
      
      private var showTextB:Boolean = false;
      
      public function LoveGiftBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["numTxt","txt","addBtnSp","itemsTag","btnSp","itemsCon","itemsConTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.numTxt);
         FontDeal.dealOne(this.itemsConTxt);
         addChild(this.btn);
         this.btn.setImg(this.btnSp);
         this.btn.setName("赠送");
         this.btn.addEventListener(MouseEvent.CLICK,this.btnClick);
         addChild(this.addBtn);
         this.addBtn.setImg(this.addBtnSp);
         this.addBtn.setName("增加");
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addBtnClick);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OVER,this.addBtnOver);
         this.addBtn.addEventListener(MouseEvent.MOUSE_OUT,this.addBtnOut);
         this.itemsConTxt.mouseEnabled = false;
         this.itemsCon.stop();
         this.itemsCon.addChild(this.mustItemsBox);
         this.itemsCon.addEventListener(MouseEvent.MOUSE_UP,this.itemsConUp);
         this.itemsCon.addEventListener(MouseEvent.MOUSE_OVER,this.itemsConOver);
         this.itemsCon.addEventListener(MouseEvent.MOUSE_OUT,this.itemsConOut);
         this.mustItemsBox.setIconPro("equipGrip",60,60);
         this.mustItemsBox.arg.init(4,1,10,0);
         this.mustItemsBox.x = 10;
         this.mustItemsBox.y = 30;
         this.mustItemsBox.evt.setWantEvent(true,true,true,true,true);
         this.mustItemsBox.setCtrlBtnImgType("BasicUI/gripDelBtn");
         this.mustItemsBox.addEventListener(ClickEvent.ON_CTRL_CLICK,this.ctrlBtnClick);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.mustItemsBox);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
         this.showBeforeGiftTalk();
      }
      
      private function showBeforeGiftTalk() : void
      {
         var d0:LoveTalkDefine = loveData.getBeforeGiftTalkDefine();
         this.startShowText(d0.getContext(Gaming.PG.da.getRoleName()));
      }
      
      private function fleshData() : void
      {
         var thingsD0:ThingsDefine = null;
         var enoughB0:Boolean = false;
         var canNum0:int = loveData.save.getCanGivingNum();
         this.numTxt.htmlText = "今日还可赠送" + ComMethod.colorEnoughNum(canNum0) + "次";
         var mustD0:MustDefine = loveData.getNowThingsMustDefine();
         if(Boolean(mustD0))
         {
            thingsD0 = Gaming.defineGroup.things.getDefine(loveData.nowThingsName);
            enoughB0 = this.mustItemsBox.inMustDefine(mustD0);
            this.itemsConTxt.visible = false;
            if(mustD0.panCondition(false) && enoughB0)
            {
               if(canNum0 > 0)
               {
                  this.btn.actived = true;
               }
               else
               {
                  this.btn.actived = false;
               }
            }
            else
            {
               this.btn.actived = false;
            }
         }
         else
         {
            this.mustItemsBox.clearAllData();
            this.itemsConTxt.visible = true;
            this.btn.actived = false;
         }
      }
      
      private function addBtnOver(e:MouseEvent) : void
      {
         var must_d0:MustDefine = Gaming.defineGroup.must.loveGiftNum;
         Gaming.uiGroup.tipBox.textTip.showFollowText(must_d0.getNumBuyText("赠送次数"));
      }
      
      private function addBtnOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      private function addBtnClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getAddNumGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_fill);
      }
      
      private function yes_fill() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         // 直接使用银币支付，不调用服务器
         Gaming.PG.da.useCrrency(price0, PriceType.COIN);
         this.do_fill();
      }
      
      private function do_fill() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         loveData.save.buyNum(da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshData();
      }
      
      private function getAddNumGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("loveGiftNum");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "loveGift";
         da0.setMaxNumLimit(loveData.save.getCanBuyNum());
         return da0;
      }
      
      private function btnClick(e:MouseEvent) : void
      {
         var mustD0:MustDefine = loveData.getNowThingsMustDefine();
         if(Boolean(mustD0))
         {
            if(mustD0.panCondition(false))
            {
               PlayerMustCtrl.deductMust(mustD0,this.yesGift);
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("物品数量不足！");
               this.fleshData();
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("物品为空！");
            this.fleshData();
         }
      }
      
      private function yesGift(v:* = null) : void
      {
         var talkD0:LoveTalkDefine = loveData.overGift();
         this.startShowText(talkD0.getContext(Gaming.PG.da.getRoleName()));
         this.fleshData();
         this.loveBox.fleshInfo();
         UIOrder.save(true,true,true,this.afterGift);
      }
      
      private function afterGift(v:* = null) : void
      {
         var v0:int = loveData.nowLoveAddValue;
         var str0:String = loveData.getRoleCn() + "对你的" + loveData.getCn() + (v0 > 0 ? ComMethod.color("+" + v0,"#00FF00") : ComMethod.color(v0 + "","#FF9900")) + "。";
         Gaming.uiGroup.alertBox.showNormal(str0,"yes");
      }
      
      private function ctrlBtnClick(e:ClickEvent) : void
      {
         loveData.clearNowThings();
         this.fleshData();
      }
      
      private function itemsConOver(e:MouseEvent) : void
      {
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         var da0:IO_ItemsData = null;
         if(Boolean(dragCtrl.dragChild))
         {
            da0 = dragCtrl.dragChild.itemsData as IO_ItemsData;
         }
         if(da0 is IO_ItemsData)
         {
            this.itemsCon.gotoAndStop(true ? "yes" : "no");
         }
         else
         {
            this.itemsCon.gotoAndStop("normal");
         }
      }
      
      private function itemsConOut(e:MouseEvent) : void
      {
         this.itemsCon.gotoAndStop("normal");
      }
      
      private function itemsConUp(e:MouseEvent) : void
      {
         var da0:IO_ItemsData = null;
         var tDa0:ThingsData = null;
         this.itemsCon.gotoAndStop("normal");
         var dragCtrl:ItemsDragController = Gaming.uiGroup.dragCtrl;
         if(Boolean(dragCtrl.dragChild))
         {
            da0 = dragCtrl.dragChild.itemsData as IO_ItemsData;
            tDa0 = da0 as ThingsData;
            if(tDa0 is ThingsData)
            {
               this.intoThings(tDa0);
            }
         }
      }
      
      private function intoThings(da0:ThingsData) : void
      {
         loveData.nowThingsName = da0.save.name;
         this.fleshData();
      }
      
      private function startShowText(str0:String) : void
      {
         this.txt.text = "";
         this.textString = str0;
         this.showTextB = true;
      }
      
      private function showTextTimer() : void
      {
         if(this.txt.length < this.textString.length)
         {
            this.txt.appendText(this.textString.charAt(this.txt.length));
         }
         else
         {
            this.overShowText();
         }
      }
      
      public function overShowText() : void
      {
         this.txt.text = this.textString;
         this.showTextB = false;
      }
      
      public function FTimer() : void
      {
         if(this.showTextB)
         {
            this.showTextTimer();
         }
      }
   }
}

