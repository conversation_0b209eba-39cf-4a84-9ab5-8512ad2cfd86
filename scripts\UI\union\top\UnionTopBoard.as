package UI.union.top
{
   import UI.bag.wear.NameChangeBox;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.page.PageBox;
   import UI.top.TopBar;
   import UI.top.TopBarBox;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.NetMethod;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.extra.MemberExtra;
   import dataAll._app.union.info.UnionInfo;
   import dataAll._app.union.info.UnionListInfo;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   
   public class UnionTopBoard extends NormalUI
   {
      
      private var barTag:Sprite;
      
      private var pageTag:Sprite;
      
      private var addBtnSp:MovieClip;
      
      private var applyBtnSp:MovieClip;
      
      private var jumpBtnSp:MovieClip;
      
      private var addBtn:NormalBtn = new NormalBtn();
      
      private var applyBtn:NormalBtn = new NormalBtn();
      
      private var jumpBtn:NormalBtn = new NormalBtn();
      
      private var maxBarNum:int = 10;
      
      private var nowPage:int = 0;
      
      private var topBox:TopBarBox = new TopBarBox();
      
      private var pageBox:PageBox = new PageBox();
      
      private var btnArr:Array = [];
      
      private var tempName:String = "";
      
      public var addUnionMustMoney:int = 200;
      
      private var nowList:UnionListInfo;
      
      private var nowApplyUnionInfo:UnionInfo;
      
      public function UnionTopBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var list0:UnionBarBtnList = null;
         elementNameArr = ["barTag","pageTag","addBtnSp","applyBtnSp","jumpBtnSp"];
         super.setImg(img0);
         this.initBar();
         addChild(this.addBtn);
         this.addBtn.setImg(this.addBtnSp);
         this.addBtn.activedAndEnabled = false;
         this.addBtn.setName("建立军队");
         this.addBtn.addEventListener(MouseEvent.CLICK,this.addBtnClick);
         addChild(this.applyBtn);
         this.applyBtn.setImg(this.applyBtnSp);
         this.applyBtn.setName("加入指定ID军队");
         this.applyBtn.addEventListener(MouseEvent.CLICK,this.applyBtnClick);
         addChild(this.jumpBtn);
         this.jumpBtn.setImg(this.jumpBtnSp);
         this.jumpBtn.setName("跳转指定页");
         this.jumpBtn.addEventListener(MouseEvent.CLICK,this.jumpBtnClick);
         Gaming.uiGroup.tipBox.textTip.addOverBody(this.addBtn,this.addUnionTip);
         for(var i:int = 0; i < this.maxBarNum; i++)
         {
            list0 = new UnionBarBtnList();
            list0.setImg(Gaming.swfLoaderManager.getResource("UnionUI","topBarBtnList"));
            list0.index = i;
            list0.clickFun = this.barBtnClick;
            this.btnArr.push(list0);
            addChild(list0);
            list0.visible = false;
         }
      }
      
      private function initBar() : void
      {
         this.topBox.initTitle("UnionUI/topBar",0);
         this.topBox.arg.init(1,this.maxBarNum,0,0);
         this.barTag.addChild(this.topBox);
         this.pageBox.setBtnUrl("BasicUI/pagePrevBtn","BasicUI/pageLongBtn");
         this.pageTag.addChild(this.pageBox);
         this.pageBox.setMaxPageShow(10);
         this.pageBox.setPageNumOut(1);
         this.pageBox.addEventListener(ClickEvent.ON_SHOW_PAGE,this.pageClick);
         this.pageBox.fleshPagePosition();
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
         this.getRankList(this.nowPage);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      private function fleshData() : void
      {
         var addUnionCondtionStr0:String = this.getAddUnionConditionText();
         this.addBtn.actived = addUnionCondtionStr0.indexOf("red") == -1;
         this.applyBtn.visible = !this.isInUnionB();
      }
      
      private function getRankList(page0:int) : void
      {
         this.nowPage = page0;
         Gaming.uiGroup.connectUI.show("获取排行榜数据……");
         Gaming.api.union.visitor.getUnionList(Gaming.getSaveIndex(),page0 + 1,this.maxBarNum,this.yes_getRankList,this.no_getRankList);
      }
      
      private function yes_getRankList(json0:String) : void
      {
         var u0:UnionListInfo = UnionListInfo.getByJson(json0);
         u0.inPageNum(this.nowPage);
         this.topBox.inData(u0);
         this.fleshBtnListByBar();
         this.pageBox.setPageNumOut(u0.getPageNum());
         this.pageBox.fleshPagePosition();
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function no_getRankList(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal("获取排行榜数据失败！\n" + str0,"yes",null,null,"no");
         Gaming.uiGroup.connectUI.hide();
      }
      
      private function pageClick(e:ClickEvent) : void
      {
         this.getRankList(e.index);
      }
      
      private function showPage(num0:int) : void
      {
         this.pageBox.showPage(num0);
      }
      
      private function fleshBtnListByBar() : void
      {
         var isInUnionB0:Boolean = false;
         var n:* = undefined;
         var list0:UnionBarBtnList = null;
         var bar0:TopBar = null;
         var joinB0:Boolean = false;
         var urlB0:Boolean = false;
         var u0:UnionInfo = null;
         isInUnionB0 = this.isInUnionB();
         for(n in this.btnArr)
         {
            list0 = this.btnArr[n];
            bar0 = this.topBox.gripArr[n];
            joinB0 = false;
            urlB0 = false;
            if(Boolean(bar0))
            {
               list0.visible = true;
               u0 = bar0.itemsData as UnionInfo;
               if(!isInUnionB0)
               {
                  joinB0 = !u0.isFillB();
               }
               urlB0 = u0.extraObj.getUrl() != "";
               list0.itemsData = bar0.itemsData as UnionInfo;
               list0.x = bar0.x + this.barTag.x;
               list0.y = bar0.y + this.barTag.y;
               list0.setJoinVisible(joinB0);
               list0.setUrlVisible(urlB0);
            }
            else
            {
               list0.visible = false;
            }
         }
      }
      
      private function barClick(e:ClickEvent) : void
      {
      }
      
      private function barBtnClick(u0:UnionInfo, label0:String) : void
      {
         var url0:String = null;
         if(label0 == "join")
         {
            this.applyUnion(u0);
         }
         else if(label0 == "url")
         {
            url0 = u0.extraObj.getUrl();
            if(url0 != "" && url0.indexOf("my.4399.com/forums/") >= 0)
            {
               NetMethod.gotoUrl(url0);
            }
         }
      }
      
      private function applyUnion(u0:UnionInfo) : void
      {
         var obj0:Object = null;
         var tip0:String = null;
         var bb0:Boolean = false;
         if(Gaming.PG.da.level >= 30)
         {
            this.nowApplyUnionInfo = u0;
            obj0 = this.nowApplyUnionInfo.getApplyTip(Gaming.PG.da);
            tip0 = obj0.tip;
            bb0 = Boolean(obj0.bb);
            Gaming.uiGroup.alertBox.showChoose(tip0,this.affter_applyUnion);
            Gaming.uiGroup.alertBox.yesBtn.actived = bb0;
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("人物等级必须超过30级才能申请加入军队。");
         }
      }
      
      private function affter_applyUnion() : void
      {
         var u0:UnionInfo = this.nowApplyUnionInfo;
         var extra0:String = Gaming.PG.da.union.extraGet.getMemberExtraString();
         Gaming.api.union.visitor.applyUnion(Gaming.getSaveIndex(),u0.unionId,extra0,this.yes_applyUnion,this.no_applyUnion);
      }
      
      private function yes_applyUnion(bb0:Boolean) : void
      {
         if(bb0)
         {
            Gaming.uiGroup.alertBox.showSuccess("你的申请已成功发送，\n请等待军队总司令的审核。");
            Gaming.testCtrl.cheating.haveUnionDataB = true;
         }
         else
         {
            this.no_addUnion("申请发送失败！");
         }
      }
      
      private function no_applyUnion(str0:String) : void
      {
         Gaming.uiGroup.unionUI.fleshLabelShow();
         Gaming.uiGroup.alertBox.showError(str0);
      }
      
      private function applyBtnClick(e:MouseEvent) : void
      {
         if(!this.isInUnionB())
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("输入要申请加入的军队ID","",this.doApplyBtn);
         }
      }
      
      private function doApplyBtn(str0:String) : void
      {
         var extra0:String = null;
         var v0:Number = Number(str0);
         if(isNaN(v0))
         {
            Gaming.uiGroup.alertBox.showError("军队ID输入不正确。");
         }
         else
         {
            extra0 = Gaming.PG.da.union.extraGet.getMemberExtraString();
            Gaming.api.union.visitor.applyUnion(Gaming.getSaveIndex(),v0,extra0,this.yes_applyUnion,this.no_applyUnion);
         }
      }
      
      private function addBtnClick(e:MouseEvent) : void
      {
         this.addUnion();
      }
      
      private function jumpBtnClick(e:MouseEvent) : void
      {
         var now0:int = this.nowPage + 1;
         var max0:int = this.pageBox.getTotalPage();
         Gaming.uiGroup.alertBox.showNumChoose("输入页码（一共" + max0 + "页）",now0,max0,1,1,this.yesJumpBtn);
      }
      
      private function yesJumpBtn(v0:int) : void
      {
         this.pageBox.showPage(v0 - 1);
      }
      
      private function isInUnionB() : Boolean
      {
         return Gaming.PG.da.union.isInUnionB();
      }
      
      private function addUnion() : void
      {
         var str0:String = null;
         if(this.addBtn.actived)
         {
            if(Bug.panParty())
            {
               return;
            }
            str0 = "建立军队需要消耗：" + ComMethod.color(this.addUnionMustMoney + " 银币","#FFFF00") + "\n请输入军队名称：";
            Gaming.uiGroup.alertBox.textInput.showTextInput(str0,"",this.affter_addUnion);
         }
      }
      
      private function affter_addUnion(str0:String) : void
      {
         this.tempName = str0;
         Gaming.uiGroup.connectUI.show("语法判断中……");
         NameChangeBox.namePan(str0,this.yes_addUnion,this.no_addUnion);
      }
      
      private function yes_addUnion() : void
      {
         Gaming.api.union.visitor.unionCreate(Gaming.getSaveIndex(),this.tempName,JSON2.encode(new MemberExtra()),this.add_addUnion,this.no_addUnion);
      }
      
      private function add_addUnion(bb0:Boolean) : void
      {
         if(bb0)
         {
            Gaming.uiGroup.connectUI.hide();
            Gaming.PG.da.main.useCoin(this.addUnionMustMoney);
            Gaming.uiGroup.mainUI.fleshCoin();
            Gaming.uiGroup.alertBox.showSuccess("创建军队成功！");
            Gaming.uiGroup.unionUI.show();
         }
         else
         {
            this.no_addUnion("该军队名称已被占用，请修改名称。");
         }
      }
      
      private function no_addUnion(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError(str0);
      }
      
      private function getAddUnionConditionText() : String
      {
         var noEnough0:String = "<red (条件不足)/>";
         var str0:String = "";
         str0 += "<b><green 建立军队条件：/></b>";
         str0 += "\n1、需要消耗银币：<yellow " + this.addUnionMustMoney + "/>" + (Gaming.PG.da.main.coin >= this.addUnionMustMoney ? "" : noEnough0);
         str0 += "\n2、没有加入军队" + (!this.isInUnionB() ? "" : noEnough0);
         return str0 + ("\n3、人物等级到达：<yellow " + "60级/>" + (Gaming.PG.da.level >= 60 ? "" : noEnough0));
      }
      
      private function addUnionTip(e:MouseEvent) : void
      {
         var str0:String = this.getAddUnionConditionText();
         if(str0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
         }
      }
      
      public function outLoginEvent() : void
      {
         this.nowApplyUnionInfo = null;
         this.nowList = null;
      }
   }
}

