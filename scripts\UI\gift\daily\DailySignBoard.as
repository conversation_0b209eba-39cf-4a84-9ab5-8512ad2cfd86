package UI.gift.daily
{
   import UI.NormalUICtrl;
   import UI.UIShow;
   import UI.api.count.ShopCountObj;
   import UI.api.shop.ShopBuyObject;
   import UI.base.NormalUI;
   import UI.base.button.NormalBtn;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.gift.GiftBox;
   import UI.base.label.LabelBox;
   import com.common.text.TextWay;
   import com.sounto.oldUtils.CalendarDefine;
   import com.sounto.oldUtils.StringDate;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll._data.ConstantDefine;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.dailySign.DailyGiftDefine;
   import dataAll.gift.dailySign.DailySignSave;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.MovieClip;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class DailySignBoard extends NormalUI
   {
      
      private var labelBox:LabelBox = new LabelBox();
      
      private var dailyBtn:NormalBtn = new NormalBtn();
      
      private var fillBtn:NormalBtn = new NormalBtn();
      
      private var giftBtn:NormalBtn = new NormalBtn();
      
      private var vipGiftBtn:NormalBtn = new NormalBtn();
      
      private var vipBtn:NormalBtn = new NormalBtn();
      
      private var giftBox:GiftBox = new GiftBox();
      
      private var vipGiftBox:GiftBox = new GiftBox();
      
      private var dailyBox:DailyBox = new DailyBox();
      
      private var monthTxt:TextField;
      
      private var numTxt:TextField;
      
      private var vipText:TextField;
      
      private var labelTag:Sprite;
      
      private var barTag:Sprite;
      
      private var giftTag:Sprite;
      
      private var vipGiftTag:Sprite;
      
      private var dailyBtnSp:MovieClip;
      
      private var fillBtnSp:MovieClip;
      
      private var giftBtnSp:MovieClip;
      
      private var vipGiftBtnSp:MovieClip;
      
      private var vipBtnSp:MovieClip;
      
      private var surpriseSp:Sprite = null;
      
      private var nowGiftLabel:String = "";
      
      private var dateStr:String = "";
      
      public function DailySignBoard()
      {
         super();
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["surpriseSp","fillBtnSp","monthTxt","numTxt","vipText","labelTag","giftTag","vipGiftTag","barTag","dailyBtnSp","giftBtnSp","vipGiftBtnSp","vipBtnSp"];
         super.setImg(img0);
         FontDeal.dealOne(this.monthTxt);
         FontDeal.dealOne(this.numTxt);
         FontDeal.dealOne(this.vipText);
         this.surpriseSp.visible = false;
         this.dealLabel();
         this.giftTag.addChild(this.giftBox);
         this.giftBox.setToNormalImg();
         this.vipGiftTag.addChild(this.vipGiftBox);
         this.vipGiftBox.setToNormalImg();
         this.dailyBox.imgType = "GiftUI/dailyIcon";
         this.dailyBox.arg.init(7,6,3,3);
         this.barTag.addChild(this.dailyBox);
         addChild(this.dailyBtn);
         this.dailyBtn.setImg(this.dailyBtnSp);
         this.dailyBtn.setName("签到");
         this.dailyBtn.addEventListener(MouseEvent.CLICK,this.signClick);
         addChild(this.fillBtn);
         this.fillBtn.setImg(this.fillBtnSp);
         this.fillBtn.setName("补签");
         this.fillBtn.addEventListener(MouseEvent.CLICK,this.fillClick);
         addChild(this.giftBtn);
         this.giftBtn.setImg(this.giftBtnSp);
         this.giftBtn.setName("领取奖励");
         this.giftBtn.addEventListener(MouseEvent.CLICK,this.getGiftClick);
         addChild(this.vipGiftBtn);
         this.vipGiftBtn.setImg(this.vipGiftBtnSp);
         this.vipGiftBtn.setName("领取VIP奖励");
         this.vipGiftBtn.addEventListener(MouseEvent.CLICK,this.getVipGiftClick);
         addChild(this.vipBtn);
         this.vipBtn.setImg(this.vipBtnSp);
         this.vipBtn.setName("成为VIP用户");
         this.vipBtn.addEventListener(MouseEvent.CLICK,this.vipClick);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function dealLabel() : void
      {
         var nameArr0:Array = Gaming.defineGroup.gift.daily.getNameArr();
         var cnNameArr0:Array = Gaming.defineGroup.gift.daily.getCnNameArr();
         this.labelBox.arg.init(10,1,-7,0);
         this.labelBox.inData("bagLabelBtn",nameArr0,cnNameArr0);
         this.labelBox.setChoose_byIndex(0);
         this.nowGiftLabel = nameArr0[0];
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.labelBox);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show("与服务器连接中……");
         Gaming.api.save.getServerTime(this.affter_getDate,this.no_getDate);
      }
      
      private function affter_getDate(time00:String) : *
      {
         Gaming.uiGroup.connectUI.hide();
         ConstantDefine.fleshNowIsDiscountB(Gaming.api.save.getNowServerDate());
         this.dateStr = TextWay.toCode(time00);
         this.fleshDaily();
         this.showGift(this.nowGiftLabel);
      }
      
      private function no_getDate(str:* = null) : *
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.giftUI.hide();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.showGift(e.label);
      }
      
      private function showGift(label0:String) : void
      {
         this.nowGiftLabel = label0;
         this.labelBox.setChoose(label0);
         var d0:DailyGiftDefine = Gaming.defineGroup.gift.daily.getByMustNum(int(label0));
         this.giftBox.inGiftAddDefineGroup(d0.gift);
         this.vipGiftBox.inGiftAddDefineGroup(d0.vipGift);
         this.vipGiftBox.hideText();
         this.giftBtn.setName("领取奖励");
         this.vipGiftBtn.setName("领取VIP奖励");
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         var haveB0:Boolean = dData.haveGetGift(label0);
         var haveVipB0:Boolean = dData.haveGetVipGift(label0);
         var signNumB0:Boolean = dData.getSignNum() >= d0.getMustNum(this.getMonthDay());
         if(signNumB0)
         {
            this.giftBtn.actived = !haveB0;
            if(haveB0)
            {
               this.giftBtn.setName("已领取奖励");
            }
            this.vipGiftBtn.actived = !haveVipB0;
            if(haveVipB0)
            {
               this.vipGiftBtn.setName("已领取VIP奖励");
            }
         }
         else
         {
            this.giftBtn.actived = false;
            this.vipGiftBtn.actived = false;
         }
         var vipB0:Boolean = Gaming.PG.da.vip.isVipB();
         this.vipGiftBox.alpha = vipB0 ? 1 : 0.5;
         this.vipBtn.visible = !vipB0;
         if(!vipB0)
         {
            this.vipGiftBtn.actived = false;
         }
         this.panAllGift();
      }
      
      private function panAllGift() : void
      {
         var n:* = undefined;
         var btn0:NormalBtn = null;
         var d0:DailyGiftDefine = null;
         var haveB0:Boolean = false;
         var haveVipB0:Boolean = false;
         var signNumB0:Boolean = false;
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         var arr0:Array = Gaming.defineGroup.gift.daily.arr;
         var vipB0:Boolean = Gaming.PG.da.vip.isVipB();
         var monthDay0:int = this.getMonthDay();
         for(n in arr0)
         {
            btn0 = this.labelBox.gripArr[n];
            d0 = arr0[n];
            haveB0 = dData.haveGetGift(d0.getName());
            haveVipB0 = dData.haveGetVipGift(d0.getName());
            signNumB0 = dData.getSignNum() >= d0.getMustNum(monthDay0);
            btn0.setSmallIcon((!haveB0 || !(haveVipB0 || !vipB0)) && signNumB0 ? "new" : "");
         }
      }
      
      private function fleshDaily() : *
      {
         var firstSignDate:StringDate = null;
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         var now_da0:StringDate = this.getNowDate();
         var saveArr:Array = dData.getSignArr();
         this.monthTxt.text = now_da0.month + 1 + "";
         if(Boolean(saveArr[0]))
         {
            firstSignDate = new StringDate();
            firstSignDate.inData_byStr(saveArr[0]);
            if(now_da0.month != firstSignDate.month || now_da0.fullYear != firstSignDate.fullYear)
            {
               dData.clearAll();
               saveArr = dData.getSignArr();
            }
         }
         this.numTxt.text = saveArr.length + "";
         var sd1:Date = now_da0.getOnlyDateClass();
         sd1.setDate(1);
         var day1:int = sd1.day;
         var dateNum0:int = CalendarDefine.getDateName(now_da0.fullYear,now_da0.month + 1);
         this.labelBox.getBtnByLabel("30").setName(dateNum0 + "次");
         var todaySignB:Boolean = this.dailyBox.inData_byDaily((day1 - 1 + 7) % 7,dateNum0,now_da0,saveArr);
         this.dailyBtn.actived = !todaySignB;
         this.dailyBtn.setName(todaySignB ? "已签到" : "签到");
      }
      
      private function getNowDate() : StringDate
      {
         var str0:String = TextWay.getText(this.dateStr);
         var sd0:StringDate = new StringDate();
         sd0.inData_byStr(str0);
         return sd0;
      }
      
      private function getMonthDay() : int
      {
         var now_da0:StringDate = this.getNowDate();
         return CalendarDefine.getDateName(now_da0.fullYear,now_da0.month + 1);
      }
      
      private function getOneGift(vipB0:Boolean) : void
      {
         var dData:DailySignSave = null;
         var mustDay0:int = 0;
         var d0:DailyGiftDefine = Gaming.defineGroup.gift.daily.getByMustNum(int(this.nowGiftLabel));
         var gift0:GiftAddDefineGroup = vipB0 ? d0.vipGift : d0.gift;
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(gift0);
         if(bb0)
         {
            dData = Gaming.PG.save.gift.daily;
            mustDay0 = d0.getMustNum(this.getMonthDay());
            if(vipB0)
            {
               dData.setGetVipGift(d0.getName(),true);
            }
            else
            {
               dData.setGetGift(d0.getName(),true);
            }
            this.showGift(this.nowGiftLabel);
            UIShow.flesh_coinChange();
         }
      }
      
      private function getGiftClick(e:MouseEvent) : void
      {
         this.getOneGift(false);
      }
      
      private function getVipGiftClick(e:MouseEvent) : void
      {
         this.getOneGift(true);
      }
      
      private function vipClick(e:MouseEvent) : void
      {
         UIShow.showApp("vip",true);
      }
      
      private function signClick(e:MouseEvent) : void
      {
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         dData.addSign(this.getNowDate().getDateStr());
         this.fleshDaily();
         this.showGift(this.nowGiftLabel);
         Gaming.uiGroup.alertBox.showSuccess("签到成功！");
      }
      
      private function fillClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getFillDailyGoodsData();
         if(da0.getMaxNumLimit() <= 0)
         {
            Gaming.uiGroup.alertBox.showError("今天已经不需要补签。");
         }
         else
         {
            Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_fill);
         }
      }
      
      private function yes_fill() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         // 直接使用银币支付，不调用服务器
         Gaming.PG.da.useCrrency(price0, PriceType.COIN);
         this.do_fill();
      }
      
      private function do_fill() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         this.fillDailyNum(da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         var countObj0:ShopCountObj = new ShopCountObj();
         countObj0.inDataByGoodsData(da0);
         Gaming.api.shopCount4399.start(1,countObj0,Gaming.PG.loginData.uid);
         countObj0.gm = 0;
         Gaming.api.shopCount4399.start(2,countObj0,Gaming.PG.loginData.uid);
         this.fleshDaily();
         this.showGift(this.nowGiftLabel);
         UIShow.flesh_coinChange();
      }
      
      private function getFillDailyGoodsData() : GoodsData
      {
         var noArr0:Array = this.getNoDailyArr();
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("fillDaily");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         da0.showTextType = "daily";
         da0.setMaxNumLimit(noArr0.length);
         return da0;
      }
      
      private function getNoDailyArr() : Array
      {
         var n:* = undefined;
         var str0:String = null;
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         var nd0:StringDate = this.getNowDate();
         var nowSignArr0:Array = dData.getSignArr();
         var arr0:Array = CalendarDefine.getDateStringArrBefore(nd0.fullYear,nd0.month + 1,nd0.date);
         var newArr0:Array = [];
         for(n in arr0)
         {
            str0 = arr0[n];
            if(nowSignArr0.indexOf(str0) == -1)
            {
               newArr0.push(str0);
            }
         }
         return newArr0;
      }
      
      private function fillDailyNum(num0:int) : void
      {
         var n:* = undefined;
         var str0:String = null;
         var dData:DailySignSave = Gaming.PG.save.gift.daily;
         var noArr0:Array = this.getNoDailyArr();
         for(n in noArr0)
         {
            if(n >= num0)
            {
               break;
            }
            str0 = noArr0[n];
            dData.addSign(str0);
         }
      }
   }
}

