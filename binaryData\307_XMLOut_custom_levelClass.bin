<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="normal">
		<gather name="">
			<level name="customBase">
				<fixed info="no" drop="no" unitG="no" eventG="no" rectG="all" />
				<info diy="customTask" enemyLv="1" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1" preSkillArr="KingRabbitTreater" />
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="僵尸王" unitType="boss" skillArr="State_AddMoveValue6" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="本我" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="wenjie">
						<unit cnName="文杰表哥" aiOrder="followBodyAttack:本我" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="zangshi">
						<unit cnName="藏师将军" aiOrder="followBodyAttack:本我" dieGotoState="stru"/>
					</unitOrder>
					<unitOrder id="girl">
						<unit cnName="小樱" aiOrder="followBodyAttack:本我" dieGotoState="stru"/>
					</unitOrder>
				</unitG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
							<order>level; diyEvent:addMainOther</order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>say; startList:s2</order>
						</event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			<![CDATA[
			
			<level name="cusWoTu">
				<info enemyLv="1" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>WoTu</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit id="weBoss" cnName="战斗僵尸" unitType="boss" skillArr="State_AddMove50" />
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="本我" lifeMul="25" dpsMul="10"  />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect><rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect><rect id="r2">2000,180,500,80</rect><rect id="r3">2500,180,500,80</rect><rect id="r4">30,30,30,30</rect>
					<rect label="addCharger">491,504,78,30</rect><rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_over</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="1"></condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r1</order>
							<order>createUnit:enemy4;r_birth </order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="cusYangMei">
				<info enemyLv="3" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>YangMei</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit id="weBoss" cnName="僵尸狙击兵" unitType="boss" skillArr="State_AddMove50" />
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="本我" lifeMul="19" dpsMul="13"  />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">173,490,246,148</rect>
					<rect id="r1">1300,757,400,60</rect>
					<rect id="r2">2357,515,455,86</rect>
					<rect id="r3">2600,-200,314,66</rect>
					<rect id="r_over">-20,-129,63,117</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">212,578,78,30</rect>
					<rect label="addCharger">2704,578,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_over</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="1"></condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r1</order>
							<order>createUnit:enemy4;r_birth </order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			<level name="cusXiaSha">
				<info enemyLv="4" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>XiaSha</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit id="weBoss" cnName="僵尸暴枪兵" unitType="boss" skillArr="State_AddMove50" />
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<unit cnName="肥胖僵尸" num="3"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="携弹僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="本我" lifeMul="17" dpsMul="15"  />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">192,60,232,120</rect>
					<rect id="r_over">2956,1073,53,113</rect>
					<rect id="r1">2944,1067,56,127</rect>
					<rect id="r2">1034,-16,300,134</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">45,111,80,46</rect>
					<rect label="addCharger">2400,838,80,46</rect>
					
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r1</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r2</order>
							<order>createUnit:enemy4; r_birth</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="cusFengWei">
				<info enemyLv="5" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>FengWei</sceneLabel>
				<drop arms="0" equip="0" skillStone="0"/>
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="we3" camp="we">
						<unit id="weBoss" cnName="携弹僵尸" unitType="boss" skillArr="State_AddMove50" />
					</unitOrder>
					<unitOrder id="we2" camp="we">
						<numberType>pro</numberType>
						<unit cnName="肥胖僵尸" num="2"/>
						<unit cnName="战斗僵尸" num="2" />
						<unit cnName="僵尸狙击兵" num="1.5"/>
						<unit cnName="僵尸暴枪兵" num="1.5"/>
					</unitOrder>
					<unitOrder id="enemy4">
						<unit cnName="本我" lifeMul="13" dpsMul="19"  />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1734,640,145,83</rect>
					<rect id="r_over">3376,1008,56,127</rect>
					<rect id="r1">20,968,442,158</rect>
					<rect id="r2">3000,968,442,158</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">514,900,145,83</rect>
					<rect label="addCharger">2600,900,145,83</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:we3; r_over</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition>say:listOver</condition></event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>createUnit:we2; r_1</order>
							<order>createUnit:enemy4; r_birth</order>
						</event>
						<event>
							<condition>enemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			
			
			
			]]>
			
		</gather>
	</father>	
	<father name="customTask">	
		<gather name="特别关卡">
			<level name="cusWoTu_doctor">
				<info diy="customTask" enemyLv="19" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>WoTu</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="战斗僵尸" unitType="boss" skillArr="State_AddMove" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="肥胖僵尸" num="5" />
						<unit cnName="战斗僵尸" num="5" />
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="本我" dieGotoState="stru" />
						<unit cnName="制毒师" aiOrder="followBodyAttack:本我"/>
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">600,290,200,117</rect><rect id="r_over">2940,563,60,125</rect>
					<rect id="r1">1500,180,500,80</rect><rect id="r2">2000,180,500,80</rect><rect id="r3">2500,180,500,80</rect><rect id="r4">30,30,30,30</rect>
					<rect label="addCharger">491,504,78,30</rect><rect label="addCharger">2507,504,78,30</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
					<group>
						<event id="e_win">
							<condition delay="1">bodyEvent:die; 制毒师</condition>
							<order>task:now; complete</order>
							<order>alert:yes; 任务成功！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; win</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="cusBeiDou_mercenary">
				<info diy="customTask" enemyLv="23" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>BeiDou</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="僵尸突击兵" unitType="boss" skillArr="State_AddMove,FightKing_hitParalysis, recovery_enemy" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="战斗僵尸" num="3" />
						<unit cnName="无头自爆僵尸" num="1" />
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="携弹僵尸" num="3" />
						<unit cnName="僵尸突击兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸炮兵总管" num="1"/>
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="本我" dieGotoState="stru" />
						<unit cnName="沙漠特种兵" num="1" aiOrder="followBodyAttack:我"/>
						<unit cnName="雇佣兵" num="2" aiOrder="followBodyAttack:沙漠特种兵" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">1555,744,174,55</rect>
					<rect id="r_over">2942,700,73,115</rect>
					<rect id="r1">933,-114,408,106</rect>
					<rect id="r2">29,-166,368,106</rect>
					<rect id="r3">2718,-88,263,106</rect>
					<rect label="addCharger">2126,170,71,60</rect>
					<rect label="addCharger">1113,374,71,60</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="cusShuangTa_defend">
				<info diy="customTask" enemyLv="27" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>ShuangTa</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="屠刀僵尸" unitType="boss" skillArr="State_AddMoveValue6" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="战斗僵尸" num="2" skillArr="hiding_enemy"/>
						<unit cnName="屠刀僵尸" num="2" skillArr="hiding_enemy"/>
						<unit cnName="携弹僵尸" num="2" skillArr="hiding_enemy"/>
						<unit cnName="僵尸突击兵" num="1" skillArr="hiding_enemy"/>
						<unit cnName="僵尸狙击兵" num="1" skillArr="hiding_enemy"/>
						<unit cnName="僵尸炮兵总管" num="1" skillArr="hiding_enemy"/>
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="制毒师" num="1" />
						<unit cnName="沙漠特种兵" num="1" aiOrder="followBodyAttack:制毒师" />
						<unit cnName="雇佣兵" num="2" aiOrder="followBodyAttack:沙漠特种兵" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			<level name="cusDongShan_QiHuang">
				<info diy="customTask" enemyLv="28" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>DongShan</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="奇皇博士" lifeMul="0.6" unitType="boss" skillArr="State_AddMoveValue6" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="战斗僵尸" num="5" />
						<unit cnName="肥胖僵尸" num="5"/>
						<unit cnName="屠刀僵尸" num="5" />
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸空军总管" num="1"  />
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="本我" dieGotoState="stru"/>
						<unit cnName="文杰表哥" dieGotoState="stru"  aiOrder="followBodyAttack:本我"/>
						<unit cnName="藏师将军"dieGotoState="stru" aiOrder="followBodyAttack:本我" />
					</unitOrder>
				</unitG>
				<!-- 区域集************************************************ -->
				<rectG>
					<rect id="r_birth">813,678,179,70</rect>
					<rect id="r_over">1930,1000,56,111</rect>
					<rect id="r1">28,1030,270,71</rect>
					<rect id="r2">1800,1030,270,71</rect>
					<rect id="r3">600,-250,270,71</rect>
					<rect id="r4">1466,-250,270,71</rect>
					<rect id="r_hide">-300,1030,270,71</rect>
					
					<!-- 弹药盆子 -->
					<rect label="addCharger">316,613,83,40</rect>
					<rect label="addCharger">1746,800,83,40</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			<level name="cusQingMing_Arthur">
				<info diy="customTask" enemyLv="30" noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"/>
				<sceneLabel>QingMing</sceneLabel>
				<drop noB="1" />
				<!-- 发兵集************************************************ -->
				<unitG>
					
					<allDefault aiOrder="patrolGlobal" skillCloseB="1" noSuperB="1"></allDefault>
					<!-- 我方 -->
					<unitOrder id="weBoss" camp="we">
						<unit id="weBoss" cnName="亚瑟" lifeMul="1" unitType="boss" skillArr="State_AddMoveValue6" />
					</unitOrder>
					<unitOrder id="weEnemy" camp="we">
						<unit cnName="屠刀僵尸" num="3" />
						<unit cnName="橄榄僵尸" num="3" />
						<unit cnName="僵尸空降兵" num="1"/>
						<unit cnName="僵尸狙击兵" num="1"/>
						<unit cnName="僵尸暴枪兵" num="1"/>
						<unit cnName="僵尸空军总管" num="1"/>
					</unitOrder>
					<unitOrder id="main">
						<unit cnName="本我" dieGotoState="stru"/>
						<unit cnName="文杰表哥" dieGotoState="stru"  aiOrder="followBodyAttack:本我"/>
						<unit cnName="藏师将军"dieGotoState="stru" aiOrder="followBodyAttack:本我" />
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1850,820,330,106</rect>
					<rect id="r_over">-30,800,80,135</rect>
					<rect id="r1">20,50,330,106</rect>
					<rect id="r2">2768,192,330,106</rect>
					<rect id="r3">1280,46,330,106</rect>
					<!-- 弹药盆子 -->
					<rect label="addCharger">2072,310,84,48</rect>
					<rect label="addCharger">122,868,84,66</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<!-- 产生主角 -->
						<event id="e1_1">
							<condition></condition>
							<order>createUnit:weBoss; r2</order>
							<order>level; diyEvent:addBoss</order>
							<order>weAllHeroNoShoot</order>
							<order>weAllHeroNoEquip</order>
							<order>heroEverParasitic:weBoss</order>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						<event><condition delay="0.3"></condition></event>
						<event>
							<condition></condition>
							<order>createUnit:main;rLeftSpider </order>
						</event>
						<event>
							<condition>liveEnemyNumber:less_1</condition>
						</event>
						<event><condition delay="0.1"></condition><order>say; startList:s2</order></event>
						<event>
							<condition delay="0.1">say:listOver</condition>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>
					</group>
					<group>
						<event id="e_fail">
							<condition delay="1">bodyEvent:die; weBoss</condition>
							<order>alert:yes; 任务失败！</order>
						</event>
						<event><condition delay="0.03"></condition><order>level; fail</order></event>
					</group>
				</eventG>
			</level>
			
			
			
			<level name="cusDongShan_shiti">
				<info enemyLv="71"  noMoreB="1" noVehicleB="1" noTreasureB="1" noPropsB="1" noDeviceB="1" mustSingleB="1"  firstLostB="1"  preSkillArr="blackHoleDevicer_1" />
				<drop noB="1" />
				<!-- 基本属性 -->
				<sceneLabel>DongShan</sceneLabel>
				<unitG>
					<unitOrder id="we2"  camp="we">
						<unit cnName="亚瑟" />
						<unit cnName="奇皇博士" />
					</unitOrder>
				</unitG>
				<!-- 区域集 --> 
				<rectG>
					<rect id="r_birth">1426,1030,142,52</rect>
					<rect id="r_over">2958,976,70,109</rect>
				</rectG>
				<!-- 事件集************************************************ -->
				<eventG>
					<group>
						<event>
							<condition></condition>
							<order>sceneMc:floor; show:shiti</order>
							<order>weAllHeroNoShoot</order><order>weAllHeroNoEquip</order>
							<order>createUnit:we2; r_birth</order><order>heroEverParasitic:亚瑟</order>
						</event>
						<event><order>openInput</order></event>
						<event><condition delay="1"></condition><order>say; startList:s1</order></event>
						<event><condition delay="0.1">say:listOver</condition></event>
						
						<event>
							<order>task:now; complete</order>
							<order>level; showPointer:r_over</order>
						</event>	
					</group>
				</eventG>
			</level>
		</gather>
	</father>
</data>