package UI.gift.anniver.gm
{
   import UI.api.shop.ShopBuyObject;
   import UI.base.AutoNormalUI;
   import UI.base.button.NormalBtn;
   import com.sounto.oldUtils.ComMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefine;
   import dataAll.gift.anniver.AnniverGmSave;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   import gameAll.level._diy.AnniverGmLevelDiy;
   
   public class AnniverGmBoard extends AutoNormalUI
   {
      
      private var gift1Sp:Sprite;
      
      private var gift2Sp:Sprite;
      
      private var giftBox1:AnniverGmGiftBox = new AnniverGmGiftBox();
      
      private var giftBox2:AnniverGmGiftBox = new AnniverGmGiftBox();
      
      private var gotoBtn:NormalBtn;
      
      private var numTxt:TextField;
      
      private var addTxt:TextField;
      
      public function AnniverGmBoard()
      {
         super();
         mcTypeArr = ["btnSp","txt"];
      }
      
      override public function setImg(img0:Sprite) : void
      {
         super.setImg(img0);
         addChild(this.giftBox1);
         this.giftBox1.setImg(this.gift1Sp);
         addChild(this.giftBox2);
         this.giftBox2.setImg(this.gift2Sp);
         this.gotoBtn.setName("挑战");
         this.addTxt.styleSheet = ComMethod.getLinkCss("#FF9900","#FFFFFF");
         this.addTxt.htmlText = ComMethod.link("添加","add");
         this.addTxt.addEventListener(MouseEvent.CLICK,this.fillClick);
         this.addTxt.visible = false;
         this.numTxt.x += 18;
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override protected function GET(str0:String) : *
      {
         return this[str0];
      }
      
      private function get save() : AnniverGmSave
      {
         return Gaming.PG.save.gift.anniverGm;
      }
      
      override public function show() : void
      {
         super.show();
         this.gotoBtn.actived = false;
         this.fleshData();
      }
      
      private function fleshData() : void
      {
         var giftArr0:Array = this.save.getGiftArr();
         var gift0:GiftAddDefineGroup = this.save.getGift(Gaming.api.save.getNowServerDate());
         this.giftBox1.inGift(giftArr0[0],gift0.name);
         this.giftBox2.inGift(giftArr0[1],gift0.name);
         var surplusNum0:int = this.save.getSurplusNum();
         this.gotoBtn.actived = surplusNum0 > 0;
         this.numTxt.htmlText = "今日剩余" + ComMethod.colorEnoughNum(surplusNum0) + "次挑战次数";
      }
      
      private function fillClick(e:MouseEvent) : void
      {
         var da0:GoodsData = this.getFillDailyGoodsData();
         Gaming.uiGroup.alertBox.shop.showCheck(da0,this.yes_fill);
      }
      
      private function yes_fill() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         // 直接使用银币支付，不调用服务器
         Gaming.PG.da.useCrrency(price0, PriceType.COIN);
         Gaming.PG.da.goods.addBuyNum(da0.def.name,da0.nowNum);
         this.do_fill();
      }
      
      private function do_fill() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         this.save.buyOne(da0.nowNum);
         Gaming.soundGroup.playSound("uiSound","changeLabel");
         this.fleshData();
      }
      
      private function getFillDailyGoodsData() : GoodsData
      {
         var da0:GoodsData = new GoodsData();
         var d0:GoodsDefine = Gaming.defineGroup.goods.getDefine("gmNum");
         da0.def = d0;
         da0.playerData = Gaming.PG.da;
         return da0;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         if(e.target == this.gotoBtn)
         {
            AnniverGmLevelDiy.chooseLevel(this.save);
         }
      }
   }
}

