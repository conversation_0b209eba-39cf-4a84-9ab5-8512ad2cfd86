<?xml version="1.0" encoding="utf-8" ?>
<data>
	<gather cnName="黑色" range="999,999,999,999"><!-- 开始等级，最大等级1，最大等级2，结束等级 -->	
		<father cnName="灰烬" color="black" name="ashesSuit" imgSwf="ashesSuit91" suitPro="hurtAll:1.15">
			<image>
				<type>head</type><itemsLevel>81</itemsLevel><blackDropLevelArr>82,83</blackDropLevelArr>
				<cnName>灰烬头盔</cnName>
				<otherObjJson>{'dpsAllBlack':0.16,'lifeAllBlack':0.75}</otherObjJson>
			</image>
			<image>
				<type>coat</type>
				<cnName>灰烬战衣</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>85</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'fightDedut':0.41}</otherObjJson>
				<addArmsType>pistol</addArmsType>
			</image>
			<image>
				<type>pants</type>
				<cnName>灰烬战裤</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>84</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.15,'moveMul':0.27}</otherObjJson>
				<addArmsType>rifle</addArmsType>
			</image>
			<image>
				<type>belt</type>
				<cnName>灰烬腰带</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>81,83</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.17, 'lifeRateBlack':11553}</otherObjJson>
				<addArmsType>rocket</addArmsType>
			</image>
		</father>
		
		<father cnName="苍鹰" color="black" name="goshawkSuit" imgSwf="goshawkSuit91" suitPro="dpsAll:1.15">
			<image>
				<type>head</type><itemsLevel>81</itemsLevel><blackDropLevelArr>82,83</blackDropLevelArr>
				<cnName>苍鹰头盔</cnName>
				<otherObjJson>{'dpsAllBlack':0.15,'dodge':0.33}</otherObjJson>
			</image>
			<image>
				<type>coat</type>
				<cnName>苍鹰战衣</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>85</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'bulletDedut':0.38}</otherObjJson>
				<addArmsType>pistol</addArmsType>
			</image>
			<image>
				<type>pants</type>
				<cnName>苍鹰战裤</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>84</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'cdMul':0.27}</otherObjJson>
				<addArmsType>rifle</addArmsType>
			</image>
			<image>
				<type>belt</type>
				<cnName>苍鹰腰带</cnName><itemsLevel>81</itemsLevel><blackDropLevelArr>81,83</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.17, 'lifeRateBlack':11553}</otherObjJson>
				<addArmsType>rocket</addArmsType>
			</image>
		</father>
		
		
		
		
		<father cnName="雷霆" color="black" name="thunderSuit" imgSwf="thunderSuit" evoB="1" suitPro="dpsAll:1.25" chipNum="50" resolveB="1">
			<image>
				<type>head</type><itemsLevel>86</itemsLevel><blackDropLevelArr>89,94</blackDropLevelArr>
				<cnName>雷霆头盔</cnName>
				<otherObjJson>{'dpsAllBlack':0.15,'lifeAllBlack':0.75}</otherObjJson>
			</image>
			<image>
				<type>coat</type>
				<cnName>雷霆战衣</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>88,93</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'fightDedut':0.41}</otherObjJson>
				<addArmsType>pistol</addArmsType>
			</image>
			<image>
				<type>pants</type>
				<cnName>雷霆战裤</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>90,95,97</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'moveMul':0.27}</otherObjJson>
				<addArmsType>rifle</addArmsType>
			</image>
			<image>
				<type>belt</type>
				<cnName>雷霆腰带</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>86,87,91,92</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.17, 'lifeRateBlack':11553}</otherObjJson>
				<addArmsType>rocket</addArmsType>
			</image>
		</father>
		<father cnName="鬼鲛" color="black" name="aintSuit" imgSwf="aintSuit" evoB="1" suitPro="hurtAll:1.25" chipNum="50" resolveB="1">
			<image>
				<type>head</type><itemsLevel>86</itemsLevel><blackDropLevelArr>89,94</blackDropLevelArr>
				<cnName>鬼鲛头盔</cnName>
				<otherObjJson>{'dpsAllBlack':0.15,'dodge':0.33}</otherObjJson>
			</image>
			<image>
				<type>coat</type>
				<cnName>鬼鲛战衣</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>88,93</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'bulletDedut':0.38}</otherObjJson>
				<addArmsType>pistol</addArmsType>
			</image>
			<image>
				<type>pants</type>
				<cnName>鬼鲛战裤</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>90,95,97</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.16,'cdMul':0.27}</otherObjJson>
				<addArmsType>rifle</addArmsType>
			</image>
			<image>
				<type>belt</type>
				<cnName>鬼鲛腰带</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr>86,87,91,92</blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.17, 'lifeRateBlack':11553}</otherObjJson>
				
				<addArmsType>rocket</addArmsType>
			</image>
		</father>
		
		
		<father cnName="神谕" color="black" name="oracleSuit" imgSwf="oracleSuit" suitPro="hurtAll:1.30" chipNum="180" resolveB="1" composeProConstB="1">
			<![CDATA[]]>
			<image>
				<type>head</type><itemsLevel>86</itemsLevel><blackDropLevelArr></blackDropLevelArr>
				<cnName>神谕头盔</cnName>
				<otherObjJson>{'dpsAllBlack':0.18,'lifeAllBlack':0.85}</otherObjJson>
				<skillArr>godHand_equip</skillArr>
				<description>秘境“狂战狼”、纪念币商店、神器宝箱、军队商店</description>
			</image>
			
			<image>
				<type>coat</type>
				<cnName>神谕战衣</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr></blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.19,'bulletDedut':0.38}</otherObjJson>
				<addArmsType>pistol</addArmsType>
				<skillArr>refraction_equip</skillArr>
				<description>神护碎片合成、纪念币商店、神器宝箱、商店</description>
				
			</image>
			<image>
				<type>pants</type>
				<cnName>神谕战裤</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr></blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.19,'cdMul':0.27}</otherObjJson>
				<addArmsType>rifle</addArmsType>
				
			</image>
			
			
			<image>
				<type>belt</type>
				<cnName>神谕腰带</cnName><itemsLevel>86</itemsLevel><blackDropLevelArr></blackDropLevelArr>
				<otherObjJson>{'dpsAllBlack':0.20, 'blackArmsDropPro':0.2}</otherObjJson>
				<skillArr>murderous_equip</skillArr>
				<addArmsType>rocket</addArmsType>
				<description>秘境“飓风巫尸”、纪念币商店、神器宝箱。</description>
			</image>
			
		</father>
		
	</gather>
</data>