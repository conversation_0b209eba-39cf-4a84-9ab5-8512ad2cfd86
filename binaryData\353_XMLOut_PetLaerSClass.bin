<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="pet" cnName="尸宠">
		<body name="雷斯" shell="compound">
			
			<name>PetLaerS</name>
			<cnName>雷斯</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/pet/PetLaerS260.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<!-- 图像 -->
			<headIconUrl>IconGather/PetLaerS</headIconUrl>
			<imgArr>
				stand,move
				,normalAttack,shootAttack,chargedAttack,ballAttack,staticAttack
				,hurt1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-14,-76,28,76</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<!-- AI属性 -->
			<nextAttackTime>0</nextAttackTime>
			<extraAIClassLabel>PetLake_AIExtra</extraAIClassLabel>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" soundUrl="sound/hand_hit">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>shootAttack</imgLabel>
					<hurtRatio>2</hurtRatio>
					<bulletLabel>shoot_PetLaerS</bulletLabel>
					<grapRect>-450,-88,351,77</grapRect>
				</hurt>
				
				<hurt info="不加入ai选择-充能">
					<imgLabel>chargedAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
				<hurt info="不加入ai选择-辐射光球">
					<imgLabel>ballAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<bulletLabel>lightBall_PetLaerS</bulletLabel>
					<grapRect>-400,-111,100,105</grapRect>
					<hurtRatio>12</hurtRatio>
				</hurt>
				<hurt info="不加入ai选择-充能">
					<imgLabel>staticAttack</imgLabel><noAiChooseB>1</noAiChooseB>
					<grapRect>-400,-111,100,105</grapRect>
				</hurt>
			</hurtArr>
		</body>	
	</father>
	<father type="zombie" cnName="雷斯">
		<bullet cnName="雷斯-射击">
			<name>shoot_PetLaerS</name>
			<cnName>雷斯-射击</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>2</bulletLife>
			<bulletWidth>40</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>0.9</attackGap>
			<attackDelay>0.5</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletAngleRange>30</bulletAngleRange>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationGap>1000</penetrationGap>
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-86,-55</shootPoint>
			<bulletSpeed>30</bulletSpeed>
			<followD value="0.3"/>	<!-- 跟踪 -->
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="30" con="filter">PetLaerS/bullet</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
		<bullet cnName="雷斯-辐射光球">
			<name>lightBall_PetLaerS</name>
			<cnName>雷斯-辐射光球</cnName>
			<!--基本属性------------------------------------------------------------ -->
			<shakeAngle>1</shakeAngle>
			<bulletLife>13</bulletLife>
			<bulletWidth>60</bulletWidth>
			<hitType>rect</hitType>
			<!--攻击时的属性------------------------------------------------------------ -->
			<attackGap>1</attackGap>
			<attackDelay>0.55</attackDelay>
			<bulletAngle>180</bulletAngle>
			<bulletNum>1</bulletNum>				
			<!--特殊属性------------------------------------------------------------ -->	
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<twoHitGap>0.1</twoHitGap>
			<followD value="1" maxTime="2" />	<!-- 跟踪 -->
			<!--运动属性------------------------------------------------------------ -->	
			<shootPoint>-92,-43</shootPoint>
			<bulletSpeed>10</bulletSpeed>
			<speedD max="25" min="1" a="-4" />
			<skillArr>lightBall_PetLake_slow,silence_wind</skillArr>
			<!--图像动画属性------------------------------------------------------------ -->
			<flipX>1</flipX>
			<bulletImgUrl raNum="1"  con="add">PetLaerS/ball</bulletImgUrl>
			<hitImgUrl soundUrl="sound/fireHit1">bulletHitEffect/energy</hitImgUrl><!-- 子弹图像【必备】 -->
		</bullet>
	</father>	
	<father name="petBodySkill">
		<skill>
			<cnName>光能</cnName>
			<name>lightLake</name>
			<iconUrl>SkillIcon/lightLake</iconUrl>
			<changeText>加成效果：[mul]</changeText>
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target systemType="hero">me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>lightLake</effectType>
			<mul>0.05</mul>
			<duration>0.5</duration>
			<range>999999</range>
			<!--图像------------------------------------------------------------ -->
			<stateEffectImg partType="mouth" con="add" raNum="1">skillEffect/lightLake</stateEffectImg>
			<description>让我方持枪单位在任何掉落物的附近都可以获得[mul]的攻击力加成和[mul]的防御加成。</description>
			<growth>
				<skill><mul>0.05</mul></skill>
				<skill><mul>0.10</mul></skill>
				<skill><mul>0.15</mul></skill>
				<skill><mul>0.20</mul></skill>
				<skill><mul>0.25</mul></skill>
				<skill><mul>0.30</mul></skill>
				<skill><mul>0.35</mul></skill>
				<skill><mul>0.40</mul></skill>
				<skill><mul>0.45</mul></skill>
			</growth>
		</skill>
	</father>
</data>