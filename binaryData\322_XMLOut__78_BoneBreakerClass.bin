<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="car" cnName="战车定义">
		<equip name="BoneBreaker" cnName="切割者"  rideLabel="WatchEagleRide">
			<evolutionLv>2</evolutionLv>
			<main label="Diggers_sub" hideB="1" dpsMul="0"/>
			<sub label="Diggers_sub" hideB="1" dpsMul="0"/>
			<lifeMul>2.5</lifeMul>
			<attackMul>2</attackMul><attackActionLabel>sprintAttack</attackActionLabel>
			<duration>15</duration>
			<cd>30</cd>
			<addObjJson>{'dpsAll':0.15,'hurtMul_pistol':0.15,'specialPartsDropPro':0.20}</addObjJson>
			<specialInfoArr>瞬秒分身和召唤单位</specialInfoArr>
			<skillArr>vehicleFit_Civilian</skillArr>
		</equip>
	</father>
	<father name="vehicle" cnName="战车body">
		<body index="0" name="切割者" shell="compound">
			
			<name>BoneBreaker</name>
			<cnName>切割者</cnName>
			<raceType>robot</raceType>
			<swfUrl>swf/vehicle/BoneBreaker.swf</swfUrl>
			<!-- 基本系数 -->
			<lifeRatio>1</lifeRatio>
			<rosRatio>1</rosRatio>
			<headHurtMul>0.5</headHurtMul>
			<!-- 图像 -->
			<dieImg soundUrl="sound/pointBoom_hero" shake="3,0.4,30">boomEffect/boom3</dieImg>
			<dieJumpMul>0</dieJumpMul>
			<imgClass>CarImage</imgClass>
			<imgType>normal</imgType>
			<rotateBySlopeB>1</rotateBySlopeB>
			<imgArr>
				stand,move,die1,sprintAttack
				,__jumpUp,jumpUp,jumpDown,jumpDown__
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-30,-88,60,88</hitRect>
			<!-- 运动 -->
			<motionD F_G="0.8" jumpDelayT="0.15" F_I="0.4" F_F="1" moveWhenVB="1" />
			<maxVx>20</maxVx>
			<maxJumpNum>1</maxJumpNum>
			<!-- 技能 -->
			<keyClass>VehicleBodyKey</keyClass>
			<skillArr>BoneBreakerHit,BoneBreakerBuff</skillArr>
			<bulletLauncherClass>VehicleBulletLauncher</bulletLauncherClass>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>move</imgLabel><hurtRatio>0.69</hurtRatio><attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>4</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown</imgLabel><hurtRatio>0.69</hurtRatio><attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>4</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpUp</imgLabel><hurtRatio>0.69</hurtRatio><attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>4</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>jumpDown__</imgLabel><hurtRatio>0.69</hurtRatio><attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>4</meBack><hitMaxNum>1</hitMaxNum>
					<hitImgUrl con="add" soundUrl="sound/vehicle_hit2">bulletHitEffect/energy</hitImgUrl>
				</hurt>
				<hurt>
					<imgLabel>sprintAttack</imgLabel><ingfollowB>1</ingfollowB>
					<hurtRatio>1.6</hurtRatio>
					<attackType>through</attackType>
					<shakeValue>10</shakeValue><meBack>10</meBack>
					<hitImgUrl con="add" soundUrl="sound/blade_hit_stone" raNum="30">bladeHitEffect/blood</hitImgUrl>
				</hurt>
				
			</hurtArr>
		</body>
		
		
		<skill>
			<name>BoneBreakerHit</name>
			<cnName>对分身造成巨大</cnName><noBeClearB>1</noBeClearB><ignoreSilenceB>1</ignoreSilenceB><ignoreImmunityB>1</ignoreImmunityB><noSkillDodgeB>1</noSkillDodgeB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>noTrueBody</otherConditionArr>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>normal_hurt</effectType>
			<extraValueType>targetMaxLife</extraValueType>
			<mul>0.1</mul>
		</skill>
		<skill>
			<name>BoneBreakerBuff</name>
			<cnName>迅齿动画</cnName><everNoClearB>1</everNoClearB><noBeClearB>1</noBeClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType>
			<effectType>actionToMove</effectType>
			<duration>99999999</duration>
			<mul>9</mul><!-- 起始帧 -->
			<value>21</value><!-- 结束帧 -->
			<secMul>7</secMul><!-- 移动距离 -->
			<valueString>sprintAttack</valueString><!-- 动画标签 -->
		</skill>
	</father>
</data>