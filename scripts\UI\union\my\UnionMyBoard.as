package UI.union.my
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.bag.ItemsGripBox;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.base.font.FontDeal;
   import UI.count.CountCtrl;
   import com.sounto.cf.NiuBiCF;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.oldUtils.StringDate;
   import com.sounto.utils.NetMethod;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.union.UnionData;
   import dataAll._app.union.define.DonationDefine;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.info.MemberInfo;
   import dataAll._app.union.info.OwnUnionInfo;
   import dataAll._app.union.info.UnionInfo;
   import dataAll.gift.GiftAddit;
   import dataAll.gift.define.GiftAddDefineGroup;
   import flash.display.Sprite;
   import flash.events.FocusEvent;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class UnionMyBoard extends BtnBox
   {
      
      private var unionTxt:TextField;
      
      private var unionValueTxt:TextField;
      
      private var meTxt:TextField;
      
      private var meValueTxt:TextField;
      
      private var noticeTxt:TextField;
      
      private var giftTag:Sprite;
      
      private var noticeSp:Sprite;
      
      private var noticeTipSp:Sprite;
      
      private var dissolveSp:Sprite;
      
      private var moneyTxt:TextField;
      
      private var otherTxt:TextField;
      
      private var quitBtn:NormalBtn;
      
      private var cancelQuitBtn:NormalBtn;
      
      private var dismissBtn:NormalBtn;
      
      private var cancelDismissBtn:NormalBtn;
      
      private var urlBtn:NormalBtn;
      
      private var noticeBtn:NormalBtn;
      
      private var getGiftBtn:NormalBtn;
      
      private var moneyDonationBtn:NormalBtn;
      
      private var otherDonationBtn:NormalBtn;
      
      private var nowTextIndex:int = -1;
      
      private var giftBox:ItemsGripBox = new ItemsGripBox();
      
      private var CF:NiuBiCF = new NiuBiCF();
      
      public function UnionMyBoard()
      {
         super();
         this.coinMax = 0;
         this.coinNum = 0;
      }
      
      public function get coinMax() : Number
      {
         return this.CF.getAttribute("coinMax");
      }
      
      public function set coinMax(v0:Number) : void
      {
         this.CF.setAttribute("coinMax",v0);
      }
      
      public function get coinNum() : Number
      {
         return this.CF.getAttribute("coinNum");
      }
      
      public function set coinNum(v0:Number) : void
      {
         this.CF.setAttribute("coinNum",v0);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var btn0:NormalBtn = null;
         elementNameArr = ["moneyTxt","otherTxt","dissolveSp","noticeTipSp","noticeSp","unionTxt","unionValueTxt","meTxt","meValueTxt","noticeTxt","giftTag"];
         super.setImg(img0);
         FontDeal.dealStaticLine(this.unionTxt);
         FontDeal.dealStaticLine(this.meTxt);
         FontDeal.dealLine(this.noticeTxt);
         FontDeal.dealOne(this.unionValueTxt);
         FontDeal.dealOne(this.meValueTxt);
         FontDeal.dealOne(this.moneyTxt);
         FontDeal.dealOne(this.otherTxt);
         addChild(this.giftBox);
         NormalUICtrl.setTag(this.giftBox,this.giftTag);
         this.giftBox.imgType = "equipGrip";
         this.giftBox.evt.setWantEvent(true,false,false,true,true);
         this.giftBox.arg.init(10,1,0,3);
         ItemsGripTipCtrl.addEvent_byItemsGripBox(this.giftBox);
         for each(btn0 in btnObj)
         {
            this[btn0.label + "Btn"] = btn0;
         }
         this.dissolveSp.visible = false;
         this.dismissBtn.setName("解散");
         this.noticeBtn.setName("确定修改公告");
         this.urlBtn.setName("修改官帖地址");
         this.getGiftBtn.setName("领取福利");
         this.getGiftBtn.activedAndEnabled = false;
         Gaming.uiGroup.tipBox.textTip.addOverBody(this.getGiftBtn,this.getGiftOver);
         this.moneyDonationBtn.setName("黄金捐献");
         this.moneyDonationBtn.activedAndEnabled = false;
         Gaming.uiGroup.tipBox.textTip.addOverBody(this.moneyDonationBtn,this.moneyDonationTip);
         this.otherDonationBtn.setName("银币捐献");
         this.quitBtn.setName("申请退出");
         this.quitBtn.activedAndEnabled = false;
         ItemsGripTipCtrl.addNormalBtnTip(this.quitBtn);
         this.cancelQuitBtn.setName("反悔退出");
         this.cancelDismissBtn.setName("取消解散");
         this.cancelDismissBtn.visible = false;
         this.noticeTxt.addEventListener(FocusEvent.FOCUS_IN,this.noticeIn);
         this.noticeTxt.addEventListener(FocusEvent.FOCUS_OUT,this.noticeOut);
         this.noticeSp.visible = false;
         this.noticeTipSp.visible = false;
         this.meValueTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
         this.meValueTxt.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
         this.unionValueTxt.addEventListener(MouseEvent.MOUSE_MOVE,this.textMove);
         this.unionValueTxt.addEventListener(MouseEvent.MOUSE_OUT,this.textOut);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.getOwnUnion();
      }
      
      private function get nowUnionInfo() : UnionInfo
      {
         return Gaming.PG.da.union.nowUnion;
      }
      
      private function get unionData() : UnionData
      {
         return Gaming.PG.da.union;
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var fun0:Function = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         if(btn0.actived)
         {
            fun0 = this[btn0.label + "Click"];
            if(fun0 is Function)
            {
               fun0();
            }
         }
      }
      
      private function getOwnUnion() : void
      {
         Gaming.uiGroup.connectUI.show("获取军队数据中……");
         Gaming.api.union.visitor.getOwnUnion(Gaming.getSaveIndex(),this.yes_getOwnUnion,this.no_getOwnUnion);
      }
      
      private function yes_getOwnUnion(jsonStr0:String) : void
      {
         var i0:OwnUnionInfo = null;
         var unionData0:UnionData = null;
         var isKingB0:Boolean = false;
         if(jsonStr0.indexOf("\"unionInfo\":null") >= 0 || !Gaming.testCtrl.cheating.haveUnionDataB)
         {
            this.noUnion();
         }
         else if(jsonStr0.indexOf("\"nickName\":null") >= 0 && jsonStr0.indexOf("\"extra\":null") >= 0)
         {
            this.noUnion();
         }
         else
         {
            i0 = new OwnUnionInfo();
            i0.inData_byJson(jsonStr0);
            if(Gaming.testCtrl.cheating.tempContribution > 0)
            {
               i0.member.contribution = Gaming.testCtrl.cheating.tempContribution;
            }
            unionData0 = this.unionData;
            unionData0.inOwnUnionInfo(i0);
            isKingB0 = unionData0.isKingB();
            this.fleshUnionInfo(unionData0.nowUnion,isKingB0,unionData0.canNoticeB());
            this.fleshMemberInfo(unionData0.nowMember,unionData0.nowUnion,isKingB0);
            this.fleshDonation();
            Gaming.uiGroup.unionUI.fleshLabelShow();
            Gaming.uiGroup.unionUI.flesher.fleshMemberInfo();
            if(unionData0.unionIsZuobiB)
            {
               Gaming.uiGroup.alertBox.showError(i0.unionInfo.zuobiPan() + "\n军队成员将无法进入任务、争霸战界面，\n也无法对军队做贡献。");
            }
         }
      }
      
      private function no_getOwnUnion(str0:String = "") : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("获取军队数据失败！\n" + str0);
         this.noUnion();
      }
      
      private function noUnion() : void
      {
         Gaming.uiGroup.unionUI.showBox("top");
         Gaming.uiGroup.unionUI.fleshLabelShow();
      }
      
      private function fleshUnionInfo(i0:UnionInfo, isKingB0:Boolean, noticeB0:Boolean) : void
      {
         var date0:StringDate = null;
         var v0:String = "";
         v0 += ComMethod.color(i0.id + "","#FFFF00");
         v0 += "\n" + ComMethod.color(i0.title,"#00FFFF");
         v0 += "\n" + ComMethod.color(i0.level + "级","#FFCC00");
         v0 += "\n" + i0.nickName;
         v0 += "\n" + i0.getExperienceString();
         v0 += "\n" + i0.getCountString();
         this.unionValueTxt.htmlText = FontDeal.getDealLeadingStr(this.unionValueTxt,v0);
         this.noticeSp.visible = noticeB0;
         this.noticeTxt.mouseEnabled = noticeB0;
         this.noticeTxt.text = i0.extraObj.getNotice();
         this.noticeOut();
         this.noticeBtn.visible = noticeB0;
         if(noticeB0)
         {
            this.urlBtn.visible = true;
            this.urlBtn.setName("修改官帖地址");
         }
         else
         {
            this.urlBtn.visible = i0.extraObj.getUrl() != "";
            this.urlBtn.setName("进入官帖");
         }
         var dissolveB0:Boolean = i0.dissolveDate != "" && i0.dissolveDate != "0";
         this.dismissBtn.visible = isKingB0 && !dissolveB0;
         this.cancelDismissBtn.visible = isKingB0 && dissolveB0;
         if(dissolveB0)
         {
            date0 = new StringDate();
            date0.inTimeValue(Number(i0.dissolveDate));
            trace("解散时间：" + date0.getStr());
         }
      }
      
      private function fleshMemberInfo(i0:MemberInfo, u0:UnionInfo, isKingB0:Boolean) : void
      {
         var v0:String = "";
         var militaryD0:MilitaryDefine = i0.getMilitaryDefine();
         v0 += i0.getRoleCnName(u0.uId);
         v0 += "\n" + militaryD0.cnName;
         v0 += "\n" + i0.getContributionString(militaryD0);
         v0 += "\n" + militaryD0.getDpsMulString() + ComMethod.color(" + ") + u0.getDpsMulString();
         this.meValueTxt.htmlText = FontDeal.getDealLeadingStr(this.meValueTxt,v0);
         this.fleshQuitBtn();
         this.giftBox.inData_byArr(militaryD0.gift.arr,"inData_gift");
         this.fleshGiftBtn();
      }
      
      private function fleshQuitBtn() : void
      {
         var max0:int = 0;
         var day0:int = 0;
         this.cancelQuitBtn.visible = false;
         this.quitBtn.actived = true;
         if(this.unionData.isKingB())
         {
            this.quitBtn.visible = false;
         }
         else
         {
            this.quitBtn.visible = true;
            max0 = this.unionData.getQuitApplyMax();
            day0 = this.unionData.getQuitApplyDay();
            if(day0 >= 0)
            {
               this.cancelQuitBtn.visible = true;
               this.quitBtn.setName("退出");
               if(day0 >= max0)
               {
                  this.quitBtn.tipString = "";
               }
               else
               {
                  this.quitBtn.actived = false;
                  this.quitBtn.tipString = "再过" + (max0 - day0) + "天才可退出军队。";
               }
            }
            else
            {
               this.quitBtn.setName("申请退出");
               this.quitBtn.tipString = "点击后，需经过" + max0 + "天，之后再点击“退出”才可真正退出军队。期间可反悔退出操作。";
            }
         }
      }
      
      private function fleshGiftBtn() : void
      {
         var getGiftB0:Boolean = this.unionData.save.todayGiftB;
         var union0:UnionData = this.unionData;
         var militaryD0:MilitaryDefine = union0.nowMember.getMilitaryDefine();
         this.getGiftBtn.visible = !union0.unionIsZuobiB;
         this.getGiftBtn.actived = !getGiftB0 && int(militaryD0.name) >= 2;
         this.getGiftBtn.setName(getGiftB0 ? "已领取" : "领取奖励");
      }
      
      private function getGiftClick() : void
      {
         var union0:UnionData = this.unionData;
         var militaryD0:MilitaryDefine = union0.nowMember.getMilitaryDefine();
         var bb0:Boolean = GiftAddit.addAndAutoBagSpacePan(militaryD0.gift,"领取奖励成功！");
         if(bb0)
         {
            union0.getTodayGift();
            this.fleshGiftBtn();
         }
      }
      
      private function getGiftOver(e:MouseEvent) : void
      {
         var union0:UnionData = this.unionData;
         var militaryD0:MilitaryDefine = union0.nowMember.getMilitaryDefine();
         var str0:String = "";
         if(int(militaryD0.name) < 2)
         {
            str0 += ComMethod.color("军衔高于列兵才可领取福利。\n","#FF0000");
         }
         str0 += "个人军衔越高，每日福利奖励就越高。";
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function fleshDonation() : void
      {
         var union0:UnionData = null;
         union0 = this.unionData;
         this.moneyTxt.htmlText = FontDeal.getDealLeadingStr(this.moneyTxt,union0.save.getDonationString(DonationDefine.MONEY));
         this.otherTxt.htmlText = FontDeal.getDealLeadingStr(this.moneyTxt,union0.save.getDonationString(DonationDefine.OTHER));
         this.moneyDonationBtn.actived = union0.save.getDonationBtnActived(DonationDefine.MONEY);
         this.otherDonationBtn.actived = union0.save.getDonationBtnActived(DonationDefine.OTHER);
         this.moneyDonationBtn.visible = !union0.unionIsZuobiB;
         this.otherDonationBtn.visible = !union0.unionIsZuobiB;
      }
      
      private function moneyDonationClick() : void
      {
         var da0:GoodsData = this.unionData.getDonationGoodsData(DonationDefine.MONEY);
         Gaming.uiGroup.alertBox.shop.showCheck(da0,this.affter_moneyDonation);
      }
      
      private function affter_moneyDonation() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         CountCtrl.addUnionContribution(da0.nowNum * 100);
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.grow.doExchange(Gaming.getSaveIndex(),da0.getPrice(),this.yes_moneyDonation,this.no_moneyDonation);
      }
      
      private function yes_moneyDonation(bb0:Boolean) : void
      {
         var d0:DonationDefine = null;
         var da0:GoodsData = null;
         var gift0:GiftAddDefineGroup = null;
         if(bb0)
         {
            Gaming.uiGroup.connectUI.hide();
            d0 = Gaming.defineGroup.union.getDonationDefine(DonationDefine.MONEY);
            da0 = Gaming.uiGroup.alertBox.shop.nowData;
            Gaming.PG.da.main.useCoin(da0.getPrice());
            Gaming.uiGroup.mainUI.fleshCoin();
            this.unionData.save.addDonationNum(DonationDefine.MONEY,da0.nowNum);
            gift0 = d0.gift.clone();
            gift0.addNumMul(da0.nowNum);
            GiftAddit.add(gift0,"捐献成功！额外获得以下物品：\n" + gift0.getDescription());
            this.fleshDonation();
            UIOrder.save(true,false,false,this.afterSave,this.afterSave);
         }
         else
         {
            this.no_moneyDonation("");
         }
      }
      
      private function no_moneyDonation(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("捐献失败！\n" + str0);
      }
      
      private function moneyDonationTip(e:* = null) : void
      {
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(DonationDefine.MONEY);
         var str0:String = "每次黄金捐献都可以额外获得：\n" + d0.gift.getDescription();
         Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
      }
      
      private function otherDonationClick() : void
      {
         var max0:int = this.unionData.save.getSurplusDonationNum(DonationDefine.OTHER);
         var d0:DonationDefine = Gaming.defineGroup.union.getDonationDefine(DonationDefine.OTHER);
         var coinMax0:int = Gaming.PG.da.main.save.coin / d0.must.coin;
         if(max0 > coinMax0)
         {
            max0 = coinMax0;
         }
         Gaming.uiGroup.alertBox.showNumChoose("请选择银币捐献次数，可以贡献" + max0 + "次",max0,max0,0,1,this.getStoreState_otherDonation);
      }
      
      private function getStoreState_otherDonation(setNum0:int) : void
      {
         var max0:int = this.unionData.save.getSurplusDonationNum(DonationDefine.OTHER);
         if(setNum0 > 0 && setNum0 <= max0)
         {
            this.coinMax = setNum0;
            this.coinNum = 0;
            Gaming.uiGroup.connectUI.show();
            Gaming.api.save.getStoreState(this.yes_getStoreState_otherDonation);
         }
      }
      
      private function yes_getStoreState_otherDonation(state0:int) : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(state0 == 1)
         {
            this.otherDonationLoop();
         }
      }
      
      private function otherDonationLoop() : void
      {
         var d0:DonationDefine = null;
         if(this.coinNum < this.coinMax)
         {
            ++this.coinNum;
            d0 = Gaming.defineGroup.union.getDonationDefine(DonationDefine.OTHER);
            Gaming.uiGroup.connectUI.show("银币捐献第" + this.coinNum + "次……");
            CountCtrl.addUnionContribution(d0.contribution);
            Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),"41",this.yes_otherDonation,this.no_otherDonation);
         }
      }
      
      private function yes_otherDonation(bb0:Boolean) : void
      {
         var d0:DonationDefine = null;
         if(bb0)
         {
            Gaming.uiGroup.connectUI.hide();
            d0 = Gaming.defineGroup.union.getDonationDefine(DonationDefine.OTHER);
            Gaming.PG.da.main.addCoin(-d0.must.coin);
            Gaming.uiGroup.mainUI.fleshCoin();
            this.unionData.save.addDonationNum(DonationDefine.OTHER);
            this.fleshDonation();
            if(this.coinNum >= this.coinMax)
            {
               this.fleshDonation();
               UIOrder.save(true,false,false,this.afterSave,this.afterSave);
            }
            else
            {
               this.otherDonationLoop();
            }
         }
         else
         {
            this.no_moneyDonation("");
         }
      }
      
      private function no_otherDonation(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("捐献终止！\n" + str0);
      }
      
      private function afterSave(e:* = null) : void
      {
         this.show();
      }
      
      private function noticeIn(e:FocusEvent) : void
      {
         this.noticeTipSp.visible = false;
      }
      
      private function noticeOut(e:FocusEvent = null) : void
      {
         if(this.noticeSp.visible)
         {
            this.noticeTipSp.visible = this.noticeTxt.text == "";
         }
         else
         {
            this.noticeTipSp.visible = false;
         }
      }
      
      private function noticeClick() : void
      {
         if(Bug.panParty())
         {
            return;
         }
         this.unionData.nowUnion.inNoticeString(this.noticeTxt.text);
         Gaming.uiGroup.unionUI.flesher.fleshUnionInfo(this.yes_noticeClick);
      }
      
      private function yes_noticeClick(data:*) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("修改公告成功！");
      }
      
      private function urlClick() : void
      {
         if(this.unionData.canNoticeB())
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("输入军队在4399群组的官方帖地址，\n用于展示军队信息和审核条件。",this.nowUnionInfo.extraObj.getUrl(),this.doUrlClick,"yesAndNo",144);
         }
         else
         {
            NetMethod.gotoUrl(this.nowUnionInfo.extraObj.getUrl());
         }
      }
      
      private function doUrlClick(str0:String) : void
      {
         var before0:String = this.nowUnionInfo.extraObj.getUrl();
         if(before0 == str0)
         {
            Gaming.uiGroup.alertBox.showError("地址未修改。");
         }
         else if(str0 != "" && str0.indexOf("my.4399.com/forums/") == -1)
         {
            Gaming.uiGroup.alertBox.showError("修改错误，改地址必须是4399群组地址！");
         }
         else
         {
            this.nowUnionInfo.inUrlString(str0);
            Gaming.uiGroup.unionUI.flesher.fleshUnionInfo(this.yesUrlClick);
         }
      }
      
      private function yesUrlClick(data:*) : void
      {
         Gaming.uiGroup.alertBox.showSuccess("修改官方帖地址成功！");
      }
      
      private function dismissClick() : void
      {
         var str0:String = "是否要解散当前军队？\n剩余提示次数：" + ComMethod.color("3","#00FF00");
         str0 = ComMethod.color(str0,"#FF9900");
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.affter_dismissClick1);
      }
      
      private function affter_dismissClick1() : void
      {
         var str0:String = "是否要解散当前军队？\n剩余提示次数：" + ComMethod.color("2","#00FF00");
         str0 = ComMethod.color(str0,"#FF9900");
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.affter_dismissClick2);
      }
      
      private function affter_dismissClick2() : void
      {
         var str0:String = "是否要解散当前军队？\n剩余提示次数：" + ComMethod.color("1","#00FF00");
         str0 = ComMethod.color(str0,"#FF9900");
         Gaming.uiGroup.alertBox.showNormal(str0,"yesAndNo",this.do_dismissClick);
      }
      
      private function do_dismissClick() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.master.dissolveUnion(Gaming.getSaveIndex(),1,this.yes_dismissClick,this.no_dismissClick);
      }
      
      private function yes_dismissClick(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showSuccess("已提交解散申请！\n系统将在" + ComMethod.color(timeStr0,"#FFFF00") + "解散当前军队。");
         this.show();
      }
      
      private function no_dismissClick(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("解散失败！\n" + str0);
      }
      
      private function cancelDismissClick() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.master.dissolveUnion(Gaming.getSaveIndex(),0,this.yes_cancelDismissClick,this.no_cancelDismissClick);
      }
      
      private function yes_cancelDismissClick(timeStr0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showSuccess("已取消军队解散。");
         this.show();
      }
      
      private function no_cancelDismissClick(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("取消解散失败！\n" + str0);
      }
      
      private function cancelQuitClick() : void
      {
         this.unionData.cancelQuit();
         this.fleshQuitBtn();
      }
      
      private function getQuitTip(num0:int) : String
      {
         var str0:String = "是否要退出当前军队？\n退出军队后，你的贡献值将归零，军衔也将消失！\n剩余提示次数：" + ComMethod.color(num0 + "","#00FF00");
         return ComMethod.color(str0,"#FF9900");
      }
      
      private function quitClick() : void
      {
         if(this.unionData.getQuitApplyDay() >= 0)
         {
            Gaming.uiGroup.alertBox.showNormal(this.getQuitTip(3),"yesAndNo",this.affter_quitClick1);
         }
         else
         {
            this.unionData.applyQuit();
            this.fleshQuitBtn();
            Gaming.uiGroup.alertBox.showSuccess("申请成功！经过" + this.unionData.getQuitApplyMax() + "天后，需再次操作，才可退出军队。");
         }
      }
      
      private function affter_quitClick1() : void
      {
         Gaming.uiGroup.alertBox.showNormal(this.getQuitTip(2),"yesAndNo",this.affter_quitClick2);
      }
      
      private function affter_quitClick2() : void
      {
         Gaming.uiGroup.alertBox.showNormal(this.getQuitTip(1),"yesAndNo",this.do_quitClick);
      }
      
      private function do_quitClick() : void
      {
         Gaming.uiGroup.connectUI.show();
         Gaming.api.union.member.quitUion(Gaming.getSaveIndex(),this.yes_quitClick,this.no_quitClick);
      }
      
      private function yes_quitClick(bb0:Boolean) : void
      {
         Gaming.uiGroup.connectUI.hide();
         if(bb0)
         {
            this.unionData.clearInfo();
            Gaming.uiGroup.alertBox.showSuccess("退出军队成功！");
            Gaming.uiGroup.unionUI.show();
         }
         else
         {
            this.no_quitClick("");
         }
      }
      
      private function no_quitClick(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("退出军队失败！\n" + str0);
      }
      
      private function textMove(e:MouseEvent) : void
      {
         var str0:String = null;
         var t0:TextField = e.target as TextField;
         var size0:int = int(t0.defaultTextFormat.size);
         var leading0:int = int(t0.defaultTextFormat.leading);
         var gap0:Number = size0 + leading0;
         var y0:Number = t0.mouseY;
         var index0:int = y0 / gap0;
         if(y0 % gap0 > size0)
         {
            index0 = -1;
         }
         if(index0 != this.nowTextIndex)
         {
            str0 = "";
            if(t0 == this.unionValueTxt)
            {
               str0 = this.unionTxtTip(index0);
            }
            else if(t0 == this.meValueTxt)
            {
               str0 = this.memberTxtTip(index0);
            }
            if(str0 != "")
            {
               Gaming.uiGroup.tipBox.textTip.showFollowText(str0);
            }
            else
            {
               Gaming.uiGroup.tipBox.hide();
            }
         }
         this.nowTextIndex = index0;
      }
      
      private function unionTxtTip(index0:int) : String
      {
         var str0:String = "";
         var u0:UnionInfo = this.unionData.nowUnion;
         var m0:MemberInfo = this.unionData.nowMember;
         if(index0 != 0)
         {
            if(index0 == 1)
            {
               str0 += "军队等级越高，对人物战斗力的加成值就越高。";
            }
            else if(index0 == 2)
            {
            }
         }
         return str0;
      }
      
      private function memberTxtTip(index0:int) : String
      {
         var str0:String = "";
         var u0:UnionInfo = this.unionData.nowUnion;
         var m0:MemberInfo = this.unionData.nowMember;
         var militaryD0:MilitaryDefine = m0.getMilitaryDefine();
         if(index0 == 1)
         {
            str0 += "军衔越高，对人物战斗力的加成值就越高。";
         }
         else if(index0 == 2)
         {
            str0 += this.unionData.getContributionCountStr(Gaming.api.save.getNowServerDate());
         }
         else if(index0 == 3)
         {
            str0 += this.unionData.getDpsMulString();
         }
         return str0;
      }
      
      private function textOut(e:MouseEvent) : void
      {
         this.nowTextIndex = -1;
         Gaming.uiGroup.tipBox.hide();
      }
   }
}

