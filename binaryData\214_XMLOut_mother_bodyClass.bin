<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="we">
		<body>
			<name>mother</name><headIconUrl>IconGather/mother</headIconUrl>
			<cnName>玉晴</cnName>
			<raceType>human</raceType>
			<swfUrl>swf/hero/mother.swf</swfUrl>
			<showLevel>9999</showLevel>
			<!-- 图像 -->
			<headPlayB>1</headPlayB>
			<imgType>normal</imgType>
			<imgArr>
				stand,move
				,normalAttack1,die1
				,__jumpUp,jumpUp,jumpUp__jumpDown,jumpDown,jumpDown__
				,__fill1_Up,fill1_Up,fill1_Up__fill1_Down,fill1_Down,fill1_Down__die1
			</imgArr>
			<!-- 碰撞体积 -->
			<hitRect>-12, -90, 24, 90</hitRect>
			<!-- 运动 -->
			<maxJumpNum>1</maxJumpNum>
			<maxVx>6</maxVx>
			<!-- 技能 -->
			<skillArr></skillArr>
			<!-- 攻击数据 -->
			<hurtArr>
				<hurt>
					<imgLabel>normalAttack1</imgLabel><cn>踢</cn>
					<hurtRatio>1</hurtRatio>
					<attackType>direct</attackType>
					<shakeValue>4</shakeValue>
					<hitImgUrl con="add" raNum="30" soundUrl="Striker/hit1">bladeHitEffect/blood</hitImgUrl>
				</hurt>
			</hurtArr>
		</body>	
	</father>
	
	
	
	
	
	<father name="task">
		<skill cnName="红光眼">
			<name>redEyes</name>
			<cnName>红光眼</cnName><noBeClearB>1</noBeClearB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target>
			<addType>state</addType><effectType>no</effectType><duration>999999</duration>
			<stateEffectImg name="redEyes" />
		</skill>
		<skill cnName="电眼外观">
			<name>BallLightningEffect</name>
			<cnName>电眼外观</cnName><noBeClearB>1</noBeClearB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target>
			<addType>state</addType><effectType>no</effectType><duration>999999</duration>
			<stateEffectImg name="paralysisEyes" />
		</skill>
		<skill>
			<name>lifeBottleIconDog</name>
			<cnName>真情治愈</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<intervalT>30</intervalT>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>lifeBottle_loveSkill</effectType>
			<!--图像------------------------------------------------------------ -->
			<description>战斗中每隔[intervalT]秒产出1瓶血瓶，拾取可以回复30%生命值，只有主角可以拾取。</description>
		</skill>
		<skill>
			<name>dieAddCharger</name>
			<cnName>死后掉落弹药箱</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>die</condition>
			<target>me</target>
			<effectProArr>0.6</effectProArr>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>chargerDrop</effectType>
		</skill>
		
		
		<bullet>
			<name>arcHowTaskFire</name>
			<cnName>地火</cnName><noMagneticB>1</noMagneticB><noBeClearB>1</noBeClearB>
			<hurtRatio>0</hurtRatio>
			<hurtMul>0.08</hurtMul>
			<attackType>holy</attackType>
			<shakeAngle>5</shakeAngle>
			<bulletLife>7</bulletLife>
			<bulletWidth>45</bulletWidth>
			<hitType>rect</hitType>
			<bulletAngle>-90</bulletAngle>
			<bulletSpeed>0</bulletSpeed>
			<!--特别属性------------------------------------------------------------ -->	
			<hitGap>0.2</hitGap>
			<penetrationNum>999</penetrationNum>
			<penetrationGap>1000</penetrationGap>
			<bulletImgUrl name="floorFireBullet"/>
			<hitImgUrl name="extremeLaserFire_hit"/>
		</bullet>
	</father>
				
</data>