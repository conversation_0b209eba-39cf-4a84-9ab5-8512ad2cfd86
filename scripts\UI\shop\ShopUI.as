package UI.shop
{
   import UI.NormalUICtrl;
   import UI.UIOrder;
   import UI.UIShow;
   import UI.api.count.ShopCountObj;
   import UI.api.shop.ShopBuyObject;
   import UI.bag.BagUI;
   import UI.bag.ItemsGrid;
   import UI.bag.ItemsGripTipCtrl;
   import UI.bag.find.ItemsFindCtrlBox;
   import UI.base.AppNormalUI;
   import UI.base.alert.AlertBox;
   import UI.base.event.ClickEvent;
   import UI.base.font.FontDeal;
   import UI.base.label.LabelBox;
   import UI.pay.PayCtrl;
   import com.sounto.utils.NumberMethod;
   import dataAll._app.goods.GoodsAddit;
   import dataAll._app.goods.GoodsData;
   import dataAll._app.goods.define.GoodsDefineGroup;
   import dataAll._app.goods.define.GoodsFatherDefine;
   import dataAll._app.goods.define.PriceType;
   import dataAll._data.ConstantDefine;
   import dataAll._player.PlayerData;
   import flash.display.MovieClip;
   import flash.display.SimpleButton;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.text.TextField;
   
   public class ShopUI extends AppNormalUI
   {
      
      private var GDG:GoodsDefineGroup = null;
      
      private var boxObj:Object = {};
      
      private var nowBox:GoodsBox = new GoodsBox();
      
      private var payBtn:SimpleButton;
      
      private var findBtn:SimpleButton;
      
      private var itemsFind:ItemsFindCtrlBox = new ItemsFindCtrlBox();
      
      public var labelBox:LabelBox = new LabelBox();
      
      public var secLabelBox:LabelBox = new LabelBox();
      
      public var priceTypeMc:MovieClip;
      
      private var coinSp:Sprite;
      
      public var tipTxt:TextField = null;
      
      public var coinTxt:TextField = null;
      
      public var moneyTxt:TextField = null;
      
      public var labelTag:Sprite = null;
      
      public var secLabelTag:Sprite = null;
      
      public var gripTag:Sprite = null;
      
      public var pageTag:Sprite = null;
      
      private var outYesFun:Function = null;
      
      public function ShopUI()
      {
         super();
         UICn = "商店";
      }
      
      override public function setImg(img0:Sprite) : void
      {
         this.GDG = Gaming.defineGroup.goods;
         elementNameArr = ["findBtn","priceTypeMc","payBtn","coinSp","labelTag","secLabelTag","gripTag","pageTag","coinTxt","moneyTxt","tipTxt"];
         super.setImg(img0);
         FontDeal.dealOne(this.coinTxt);
         FontDeal.dealOne(this.moneyTxt);
         this.priceTypeMc.stop();
         addChildAt(img,0);
         this.payBtn.addEventListener(MouseEvent.CLICK,PayCtrl.gotoPay);
         addChild(this.labelBox);
         NormalUICtrl.setTag(this.labelBox,this.labelTag);
         this.labelBox.arg.init(7,1,-10,0);
         this.labelBox.inData("bagLabelBtn",this.GDG.fatherNameArrObj["shop"],this.GDG.fatherCnNameArrObj["shop"]);
         this.labelBox.setChoose_byIndex(0);
         this.labelBox.addEventListener(ClickEvent.ON_CLICK,this.labelClick);
         addChild(this.secLabelBox);
         NormalUICtrl.setTag(this.secLabelBox,this.secLabelTag);
         this.secLabelBox.arg.init(10,1,0,0);
         this.secLabelBox.addEventListener(ClickEvent.ON_CLICK,this.secLabelClick);
         this.tipTxt.visible = false;
         this.coinTxt.addEventListener(MouseEvent.MOUSE_OVER,this.coinOver);
         this.coinTxt.addEventListener(MouseEvent.MOUSE_OUT,this.coinOut);
         this.setCoinVisible(false);
         this.findBtn.addEventListener(MouseEvent.CLICK,this.findClick);
         this.itemsFind.setToNormalImg();
         this.itemsFind.y = 413;
         this.itemsFind.hide();
         this.init_addBox();
      }
      
      private function init_addBox() : void
      {
         var n:* = undefined;
         var name0:String = null;
         var box0:GoodsBox = null;
         var arr0:Array = this.GDG.fatherNameArrObj["shop"];
         for(n in arr0)
         {
            name0 = arr0[n];
            box0 = new GoodsBox();
            box0.setIconPro("ShopUI/shopGrip",50,50);
            box0.arg.init(3,5,3,3);
            box0.evt.setWantEvent(true,false,false,true,true);
            box0.x = this.gripTag.x;
            box0.y = this.gripTag.y;
            box0.visible = false;
            box0.addEventListener(ClickEvent.ON_CLICK,this.gripClick);
            ItemsGripTipCtrl.addEvent_byItemsGripBox(box0);
            box0.pageBox.setToNormalBtn();
            box0.setPagePos(this.pageTag);
            addChild(box0);
            this.boxObj[name0] = box0;
         }
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function setCoinVisible(bb0:Boolean) : void
      {
         this.coinSp.visible = bb0;
         if(bb0)
         {
            this.coinSp["txt"].text = NumberMethod.toBigWan(Gaming.PG.da.main.save.coin);
         }
      }
      
      public function fleshData() : void
      {
         if(this.visible)
         {
            this.showBox(this.labelBox.nowLabel,"",this.secLabelBox.nowLabel);
         }
      }
      
      public function fleshPrice() : void
      {
         Gaming.PG.fleshGoodsPrice();
         if(visible)
         {
            this.fleshCoin();
            this.nowBox.doChildFun("fleshGoodsPrice");
         }
      }
      
      public function fleshCoin() : void
      {
         var f0:GoodsFatherDefine = null;
         var PD:PlayerData = null;
         var priceType0:String = null;
         if(visible)
         {
            f0 = Gaming.defineGroup.goods.getFatherDefine(this.labelBox.nowLabel);
            PD = Gaming.PG.da;
            priceType0 = PriceType.COIN;  // 强制使用银币
            this.moneyTxt.visible = false;
            this.setCoinVisible(true);
            this.priceTypeMc.gotoAndStop(PriceType.getPriceIconMcLabel(priceType0));
            this.coinTxt.htmlText = PriceType.color(NumberMethod.toWan(PD.getCrrency(priceType0)),priceType0);
         }
      }
      
      private function coinOver(e:MouseEvent) : void
      {
         var tip0:String = PriceType.getInfo(this.labelBox.nowLabel);
         if(tip0 != "")
         {
            Gaming.uiGroup.tipBox.textTip.showFollowText(tip0);
         }
      }
      
      private function coinOut(e:MouseEvent) : void
      {
         Gaming.uiGroup.tipBox.hide();
      }
      
      override public function show() : void
      {
         super.show();
         Gaming.uiGroup.connectUI.show();
         Gaming.api.save.getServerTime(this.yes_GetTime,this.no_GetTime);
      }
      
      public function gotoLabel(label0:String) : void
      {
         this.labelBox.nowLabel = label0;
         UIShow.showApp("shop");
      }
      
      private function yes_GetTime(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         ConstantDefine.fleshNowIsDiscountB(Gaming.api.save.getNowServerDate());
         this.fleshData();
         var bag0:BagUI = Gaming.uiGroup.bagUI;
         if(!bag0.visible)
         {
            bag0.showAndLabel("things");
         }
      }
      
      private function no_GetTime(str0:String) : void
      {
         Gaming.uiGroup.connectUI.hide();
         Gaming.uiGroup.alertBox.showError("与服务器连接失败，无法进入商店！");
         this.hide();
      }
      
      override public function hide() : void
      {
         if(this.visible && Boolean(this.nowBox))
         {
            this.nowBox.setChoose_byIndex(-1);
         }
         if(Gaming.uiGroup.bagUI.visible)
         {
            Gaming.uiGroup.bagUI.hide();
         }
         super.hide();
      }
      
      private function labelClick(e:ClickEvent) : void
      {
         this.fleshShow(e.label,this.secLabelBox.nowLabel);
      }
      
      private function secLabelClick(e:ClickEvent) : void
      {
         this.fleshShow(this.labelBox.nowLabel,e.label);
      }
      
      public function showBox(label0:String, showChooseName0:String = "", secLabel0:String = "all") : void
      {
         this.fleshShow(label0,secLabel0,showChooseName0);
      }
      
      private function fleshShow(label0:String, secLabel0:String = "all", showChooseName0:String = "") : void
      {
         var f0:GoodsFatherDefine = Gaming.defineGroup.goods.getFatherDefine(label0);
         this.fleshBoxShow(label0);
         secLabel0 = this.setSecLabel(f0,secLabel0);
         var dataArr0:Array = Gaming.PG.da.goods.getDataArr(label0,secLabel0);
         this.nowBox.inData_byArr(dataArr0,"inData_goods");
         if(showChooseName0 == "")
         {
            this.nowBox.setChoose_byIndex(-1);
         }
         else
         {
            this.showChooseByName(showChooseName0);
         }
         var tip0:String = PriceType.getBarTip(label0);
         if(tip0 != "")
         {
            this.tipTxt.htmlText = tip0;
            this.tipTxt.visible = true;
         }
         else
         {
            this.tipTxt.visible = false;
         }
         this.fleshPrice();
      }
      
      private function fleshBoxShow(label0:String) : void
      {
         var n:* = undefined;
         this.labelBox.setChoose(label0);
         for(n in this.boxObj)
         {
            this.boxObj[n].visible = false;
         }
         this.nowBox = this.boxObj[label0];
         this.nowBox.visible = true;
         if(this.itemsFind.visible)
         {
            this.itemsFind.setNewItemsBox(this.nowBox);
         }
      }
      
      private function setSecLabel(f0:GoodsFatherDefine, secLabel0:String) : String
      {
         this.secLabelBox.clear();
         this.secLabelBox.inData("ShopUI/secLabelBtn2",f0.labelArr,f0.getLabelCnArr());
         if(f0.labelArr.indexOf(secLabel0) == -1)
         {
            secLabel0 = f0.labelArr[0];
         }
         this.secLabelBox.setChoose(secLabel0);
         return secLabel0;
      }
      
      private function showChooseByName(name0:String) : void
      {
         var n:* = undefined;
         var grip0:ItemsGrid = null;
         var da0:GoodsData = null;
         var gripArr0:Array = this.nowBox.gripArr;
         for(n in gripArr0)
         {
            grip0 = gripArr0[n];
            da0 = grip0.itemsData as GoodsData;
            if(da0 is GoodsData)
            {
               if(da0.def.name == name0)
               {
                  this.nowBox.setChoose_byIndex(grip0.index);
                  return;
               }
            }
         }
      }
      
      private function gripClick(e:ClickEvent) : void
      {
         var da0:GoodsData = e.childData as GoodsData;
         this.buyData(da0);
      }
      
      public function buyDefineName(defineName0:String, yesFun0:Function = null, setNum0:int = 0) : void
      {
         var da0:GoodsData = Gaming.PG.da.goods.getData(defineName0);
         if(Boolean(da0))
         {
            if(da0.getShowB())
            {
               this.outYesFun = yesFun0;
               this.buyData(da0,setNum0);
            }
            else
            {
               this.outYesFun = null;
               Gaming.uiGroup.alertBox.showError("该商品已下架！");
            }
         }
      }
      
      private function buyData(da0:GoodsData, setNum0:int = 0) : void
      {
         var alertBox0:AlertBox = Gaming.uiGroup.alertBox;
         var limit_str0:String = ShopBuyLimit.getLimit(da0);
         if(limit_str0 != "")
         {
            this.outYesFun = null;
            alertBox0.showNormal(limit_str0,"yes",null,null,"no");
         }
         else
         {
            alertBox0.shop.showCheck(da0,this.yes_buy,null,setNum0);
         }
      }
      
      private function yes_buy() : void
      {
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         Gaming.PG.da.goods.addBuyNum(da0.def.name,da0.nowNum);
         // 统一使用银币支付，不再区分货币类型
         this.do_buy();
      }
      
      private function do_buy() : void
      {
         Gaming.uiGroup.connectUI.hide();
         var da0:GoodsData = Gaming.uiGroup.alertBox.shop.nowData;
         var price0:Number = da0.getPrice();
         // 统一使用银币扣除
         Gaming.PG.da.useCrrency(price0, PriceType.COIN);

         GoodsAddit.addByGoodsData(da0,true);
         this.countNum(da0);
         var countObj0:ShopCountObj = new ShopCountObj(da0);
         Gaming.api.shopCount4399.start(1,countObj0,Gaming.PG.loginData.uid);
         if(countObj0.pt == 3)
         {
            countObj0.gm = 0;
            Gaming.api.shopCount4399.start(2,countObj0,Gaming.PG.loginData.uid);
         }
         if(da0.def.inBagType == "things")
         {
            Gaming.uiGroup.gameWorldUI.propsNumChangeEvent();
         }
         UIOrder.outShowSaveTip();
         Gaming.soundGroup.playSound("uiSound","buy");
         this.fleshPrice();
         Gaming.uiGroup.mainUI.fleshCoin();
         if(visible)
         {
            Gaming.uiGroup.bagUI.showAndLabel(da0.def.inBagType);
         }
         else if(!(this.outYesFun is Function))
         {
            Gaming.uiGroup.reshowNowApp();
         }
         if(this.outYesFun is Function)
         {
            this.outYesFun(da0);
            this.outYesFun = null;
         }
      }
      
      private function countNum(da0:GoodsData) : void
      {
         var cn0:String = da0.def.cnName;
         if(cn0 == "军备物资")
         {
            Gaming.PG.da.union.building.saveGroup.buySuppliesNum += da0.getTrueNum();
         }
      }
      
      private function findClick(e:MouseEvent) : void
      {
         addChild(this.itemsFind);
         if(this.secLabelBox.nowLabel != "all")
         {
            this.fleshShow(this.labelBox.nowLabel,"all");
         }
         this.itemsFind.showBox(this.nowBox,Gaming.defineGroup.goods);
      }
   }
}

