<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="score" cnName="积分" priceType="score" labelArr="all">
		<type>
			<!-- 物品 -->
			<goods cnName="超能石" name="skillStone_score" defineLabel="skillStone" price="50" chooseNumB="1" num="10"><priceType>score</priceType></goods>
			<goods cnName="神能石" name="godStone_score" defineLabel="godStone" price="200" chooseNumB="1" num="10"><priceType>score</priceType></goods>
			<goods cnName="转化石" name="converStone_score" defineLabel="converStone" price="200" chooseNumB="1" num="10"><priceType>score</priceType></goods>
			<goods cnName="商券" name="taxStamp_score" defineLabel="taxStamp" price="100" chooseNumB="1" num="10"><priceType>score</priceType></goods>
			
			<goods cnName="普通装备箱" name="equipBox_score" defineLabel="equipBox" price="15" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级装备箱" name="equipHighBox_score" defineLabel="equipHighBox" price="60" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="普通武器箱" name="armsBox_score" defineLabel="armsBox" price="15" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级武器箱" name="armsHighBox_score" defineLabel="armsHighBox" price="60" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级步枪箱" name="rifleBox_score" defineLabel="rifleBox" price="80" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级狙击箱" name="sniperBox_score" defineLabel="sniperBox" price="80" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级手枪箱" name="pistolBox_score" defineLabel="pistolBox" price="80" chooseNumB="1"><priceType>score</priceType></goods>
			<goods cnName="高级散弹箱" name="shotgunBox_score" defineLabel="shotgunBox" price="80" chooseNumB="1"><priceType>score</priceType></goods>
		</type>
			
		</father>
</data>