<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="loveSkill" cnName="忠诚度技能">
		<skill>
			<name>dodgeProZang</name><cnName>闪避提升20%</cnName><everNoClearB>1</everNoClearB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<value>0.2</value><effectType>dodgePro</effectType>
			<description>战斗中提升20%的闪避。</description>
		</skill>
		<skill>
			<name>defenceZang</name><cnName>防御力提升30%</cnName><everNoClearB>1</everNoClearB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<mul>0.7</mul><effectType>underHurtMul</effectType>
			<description>战斗中提升[1-mul]的防御力。</description>
		</skill>
		<skill>
			<name>hurtStrikerZang</name><cnName>P1增伤-藏师</cnName><everNoClearB>1</everNoClearB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<mul>1.1</mul><effectType>hurtMul</effectType>
			<valueString>Striker</valueString>
			<description>战斗中提升P1角色[mul-1]的攻击力。</description>
		</skill>
		<skill><!-- dps -->
			<name>noHurtZang</name>
			<cnName>抵挡伤害</cnName><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType><effectProArr>0.3</effectProArr>
			<condition>underHit</condition>
			<otherConditionArr>hurtMoreLifePer</otherConditionArr>
			<conditionRange>0.3</conditionRange>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>noHurt</effectType>
			<description>有[effectProArr.0]的几率抵挡超过自身生命值30%的伤害。</description>
			<targetEffectImg partType="body">generalEffect/hurtDefence</targetEffectImg>
		</skill>
		<skill>
			<name>sameArmsHurtAddZang</name>
			<cnName>相同武器伤害叠加</cnName>
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target systemType="hero" noMeB="1">me,range,we</target>
			<addType>state</addType>
			<effectType>sameArmsHurtAdd</effectType>
			<mul>0.15</mul>
			<duration>0.5</duration>
			<range>9999999</range>
			<stateEffectImg partType="shootPoint" con="filter" raNum="30" followPartRaB="1">skillEffect/smallFire</stateEffectImg>
			<description>队友手中持有和藏师相同枪支时，攻击力提升[mul]。</description>
		</skill>
		<skill>
			<name>hurtZang</name><cnName>攻击力提升30%</cnName><everNoClearB>1</everNoClearB>
			<conditionType>passive</conditionType><condition>add</condition><target>me</target><addType>state</addType><duration>99999</duration>
			<mul>1.3</mul><effectType>hurtMul</effectType>
			<description>战斗中的攻击力提升[mul-1]。</description>
		</skill>
		<skill>
			<name>hurtHoleZang</name>
			<cnName>伤害光环150</cnName>
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target noMeB="1">me,range,we</target>
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>1.4</mul>
			<duration>0.2</duration>
			<range>150</range>
			<stateEffectImg partType="2hand" con="add" raNum="15" followPartRaB="1">skillEffect/murderous_enemy</stateEffectImg>
			<description>提升周围[range]码内队友[mul-1]的攻击力。</description>
		</skill>
	</father>
</data>