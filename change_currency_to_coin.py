#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修改商店货币类型为银币的脚本
将所有商品的价格类型改为 coin (银币)
"""

import os
import re
import shutil
from pathlib import Path

def backup_file(file_path):
    """备份原文件"""
    backup_path = str(file_path) + ".backup"
    shutil.copy2(file_path, backup_path)
    print(f"已备份: {backup_path}")

def change_price_type_to_coin(content):
    """将XML内容中的priceType改为coin"""

    # 1. 修改 <priceType>标签内容
    content = re.sub(r'<priceType>[^<]*</priceType>', '<priceType>coin</priceType>', content)

    # 2. 修改 priceType属性
    content = re.sub(r'priceType="[^"]*"', 'priceType="coin"', content)

    # 3. 修改father标签中的priceType属性
    content = re.sub(r'(<father[^>]*priceType=")[^"]*(")', r'\1coin\2', content)

    return content

def modify_code_files():
    """修改代码文件中的硬编码逻辑"""

    print("\n修改代码文件...")

    # 已经通过str-replace-editor修改了以下文件：
    # 1. scripts\dataAll\_app\goods\define\GoodsDefine.as
    # 2. scripts\UI\shop\ShopUI.as
    # 3. scripts\UI\shop\GoodsGrip.as

    print("✓ 代码文件修改完成")
    print("  - GoodsDefine.as: 修改自动货币设置逻辑")
    print("  - ShopUI.as: 修改商店界面货币显示和购买逻辑")
    print("  - GoodsGrip.as: 修改商品价格显示逻辑")

def process_goods_files():
    """处理所有商品配置文件"""
    
    # 需要修改的文件列表
    files_to_modify = [
        "binaryData/281_XMLOut_goodsClass.bin",           # 主要商品
        "binaryData/8_XMLOut_activeGoodsClass.bin",       # 活动商品
        "binaryData/320_XMLOut_anniCoinClass.bin",        # 纪念币商店
        "binaryData/85_XMLOut_tenCoinClass.bin",          # 十年币商店
        "binaryData/264_XMLOut_demBallClass.bin",         # 万能球商店
        "binaryData/80_XMLOut_goodsDelClass.bin",         # 已删除商品
        "binaryData/349_XMLOut_exploitCardsClass.bin",    # 军队商店
        "binaryData/84_XMLOut_otherGoodsClass.bin",       # 其他商品
        "binaryData/88_XMLOut_partsCoinClass.bin",        # 零件券商店
        "binaryData/360_XMLOut_scoreGoodsClass.bin",      # 积分商店
    ]
    
    modified_count = 0
    
    for file_path in files_to_modify:
        if os.path.exists(file_path):
            print(f"\n处理文件: {file_path}")
            
            # 备份原文件
            backup_file(file_path)
            
            # 读取文件内容
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 修改内容
                original_content = content
                modified_content = change_price_type_to_coin(content)
                
                # 检查是否有修改
                if original_content != modified_content:
                    # 写入修改后的内容
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(modified_content)
                    
                    print(f"✓ 已修改: {file_path}")
                    modified_count += 1
                else:
                    print(f"- 无需修改: {file_path}")
                    
            except Exception as e:
                print(f"✗ 处理失败: {file_path} - {e}")
        else:
            print(f"✗ 文件不存在: {file_path}")
    
    return modified_count

def show_price_type_changes():
    """显示价格类型的变化说明"""
    print("=" * 60)
    print("货币类型修改说明:")
    print("=" * 60)
    print("原货币类型 -> 新货币类型")
    print("-" * 30)
    print("money (黄金)      -> coin (银币)")
    print("score (积分)      -> coin (银币)")
    print("anniCoin (纪念币) -> coin (银币)")
    print("tenCoin (十年币)  -> coin (银币)")
    print("demBall (万能球)  -> coin (银币)")
    print("partsCoin (零件券) -> coin (银币)")
    print("exploitCards (军队功勋牌) -> coin (银币)")
    print("arenaStamp (优胜券) -> coin (银币)")
    print("taxStamp (商券)   -> coin (银币)")
    print("zongzi (粽子)     -> coin (银币)")
    print("pumpkin (南瓜)    -> coin (银币)")
    print("=" * 60)

def main():
    """主函数"""
    print("商店货币类型批量修改工具")
    print("将所有商品的货币类型改为银币 (coin)")
    print("=" * 60)
    
    # 显示修改说明
    show_price_type_changes()
    
    # 确认操作
    confirm = input("\n是否继续执行修改? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("操作已取消")
        return
    
    # 执行修改
    print("\n开始处理文件...")
    modified_count = process_goods_files()

    # 修改代码文件
    modify_code_files()

    print(f"\n修改完成!")
    print(f"共修改了 {modified_count} 个XML文件")
    print("已修改 3 个代码文件")
    print("\n注意事项:")
    print("1. 原文件已备份为 .backup 后缀")
    print("2. 如需恢复，请将 .backup 文件重命名回原文件名")
    print("3. 修改后需要重新编译游戏才能生效")
    print("4. 代码修改包括:")
    print("   - 商品定义的自动货币设置")
    print("   - 商店界面的货币显示逻辑")
    print("   - 商品购买的货币扣除逻辑")

if __name__ == "__main__":
    main()
