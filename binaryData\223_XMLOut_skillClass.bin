<?xml version="1.0" encoding="utf-8" ?>
<data>
	<father name="dropEffect" cnName="掉落的效果物品">
		<skill index="0" name="增加血量">
			<name>DropEffect_AddLifeMul</name>
			<cnName>增加血量</cnName>
			<ignoreImmunityB>1</ignoreImmunityB>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>dropAddLife</effectType>
			<mul>0.3</mul>
		</skill>
		<skill index="0" name="增加弹药">
			<name>DropEffect_AddChargerMul</name>
			<cnName>增加弹药</cnName>
			<ignoreImmunityB>1</ignoreImmunityB>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>charger</effectType>
			<mul>0.5</mul>
			<!--附加属性------target对哪个武器加弹药：now当前、all所有 -->
			<obj>"target":"all","addType":"add"</obj>
		</skill>
		<skill name="无敌药水">
			<name>invincibleDrugDrop</name>
			<cnName>无敌药水</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>5</duration>
			<stateEffectImg partType="body">generalEffect/invincibleShield</stateEffectImg>
		</skill>
		<skill>
			<name>invinCannedDrop</name>
			<cnName>无敌罐头</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>10</duration>
			<meEffectImg soundUrl="sound/energyShield"></meEffectImg>
			<stateEffectImg partType="body">generalEffect/invincibleShield</stateEffectImg>
		</skill>
		<skill>
			<name>hurtDrugDrop</name>
			<cnName>增伤药剂</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>allHurtMul</effectType>
			<mul>2</mul>
			<duration>7</duration>
			<meEffectImg soundUrl="sound/murderous_hero"></meEffectImg>
			<stateEffectImg name="murderous_enemy_state"/>
		</skill>
		
		
		
		
		
		
		<skill index="0" name="增加移动速度">
			<name>DropEffect_AddMoveSpeedMul</name>
			<cnName>增加移动速度</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>1.3</mul>
			<duration>5</duration>
			<range>0</range>
		</skill>
		<skill index="0" cnName="召唤摩卡"><!-- 限制 -->
			<name>addMocha</name>
			<cnName>召唤摩卡</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnitsMocha</effectType>
			<summonedUnitsB>1</summonedUnitsB>
			<!-- 子弹所需 -->
			<obj>"cnName":"摩卡","num":1,"lifeMul":1.7,"dpsMul":0.3,"mulByFatherB":1,"skillArr":["escapeInvincible"]</obj>
			<!--图像------------------------------------------------------------ -->
			<description>蝙蝠。</description>
		</skill>
	</father>	
	<father name="thingsEffect" cnName="物品使用效果">
		<skill>
			<name>zombieBottle</name>
			<cnName>僵尸血瓶</cnName><everNoClearB>1</everNoClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<conditionType>active</conditionType><noBeClearB>1</noBeClearB>
			<target>me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>noAiFind</effectType>
			<duration>999999</duration>
			<range>99999</range>
			
			<description>给在场所有我方单位抹上僵尸血液，使其不会成为敌人的攻击目标。</description>
		</skill>
		<skill>
			<name>glutinous</name>
			<cnName>辣椒糯米饭</cnName><everNoClearB>1</everNoClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<conditionType>active</conditionType><noBeClearB>1</noBeClearB>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>glutinous</effectType>
			<mul>1.5</mul>
			<duration>999999</duration>
			<stateEffectImg name="mouthSmoke"/>
			<description>无限怒气。</description>
		</skill>
		
		<skill><!-- dps -->
			<name>iconGiftBox</name>
			<cnName>圣诞铁箱</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>summonedUnits</effectType>
			<obj>"cnName":"圣诞铁箱","num":1,"lifeMul":1000,"maxNum":1,"noUnderHurtB":1,"pointEf":1</obj>
			<pointEffectImg name="oreBombShowFlower"/>
			<addSkillEffectImg name="iconGiftBoxBuff"/>
			<duration>120</duration>
			<!--图像------------------------------------------------------------ -->
			<description>。</description>
		</skill>
					<skill><!-- dps -->
						<name>iconGiftBoxBuff</name>
						<cnName>圣诞铁箱-buff</cnName><noBeClearB>1</noBeClearB><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>passive</conditionType>
						<condition>interval</condition><intervalT>0.1</intervalT>
						<otherConditionArr>targetNoNowBuffUnlessMe</otherConditionArr>
						<target>me,range,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>stateAndInstant</addType>
						<effectType>lockTargetMe</effectType>
						<duration>1</duration>
						<range>99999</range>
						<!--图像------------------------------------------------------------ -->
						<stateEffectImg name="FoggyShakeHit_state"/>
						<description>强制吸引周围[range]码以内的敌方攻击自己。</description>
					</skill>
		
		
		<skill cnName="无敌隐身-重生石"><!-- 限制 -->
			<name>godHiding_things</name>
			<cnName>无敌隐身-重生石</cnName><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB><ignoreNoSkillB>1</ignoreNoSkillB><ignoreImmunityB>1</ignoreImmunityB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>godHiding_things</effectType>
			<value>1</value>
			<duration>3</duration>
			<!--图像 ------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/hiding_hero" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>释放技能后，单位进入无敌隐身状态，持续[duration]秒。如果你对敌人发起攻击会打破隐身状态。</description>
		</skill>
		<skill index="0" name="godHiding_Pet" cnName="无敌隐身"><!-- 限制 -->
			<name>godHiding_Pet</name>
			<cnName>无敌隐身</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>godHidingB</effectType>
			<value>1</value>
			<duration>3</duration>
			<!--图像 ------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/hiding_hero" con="add">skillEffect/hiding_hero</meEffectImg>
			<description>释放技能后，单位进入无敌隐身状态，持续[duration]秒。如果你对敌人发起攻击会打破隐身状态。</description>
		</skill>
		
		<skill index="0" name="moonCake" cnName="增加攻击力"><!-- 限制 -->
			<name>moonCake</name>
			<cnName>月饼增加攻击力</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>2</mul>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<stateEffectImg partType="2hand" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
			<description>使用后将增加战斗中100%的攻击力。</description>
		</skill>
		
		<skill index="0" name="tangyuan" cnName="汤圆攻击力"><!-- 限制 -->
			<name>tangyuan</name>
			<cnName>汤圆增加攻击力</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>2</mul>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<stateEffectImg partType="2hand" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
			<description>使用后将增加战斗中100%的攻击力。</description>
		</skill>
		<skill><!-- 限制 -->
			<name>nineCake</name>
			<cnName>九层糕增加射速</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>attackGapMul</effectType>
			<mul>2</mul>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<stateEffectImg name="globalSpurting_hero_state2"/>
			<description>使用后将增加战斗中100%的射速。</description>
		</skill>
		
		<skill index="0" name="jiaozi" cnName="饺子无敌状态"><!-- 限制 -->
			<name>jiaozi</name>
			<cnName>饺子无敌状态</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>invincible</effectType>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<stateEffectImg con="add">skillEffect/energyShield</stateEffectImg>
			<description>使用无敌。</description>
		</skill>
		<skill name="armsDropDouble" cnName="双倍黑色武器掉落"><!-- 限制 -->
			<name>armsDropDouble</name>
			<cnName>双倍黑色武器掉落</cnName>
			<conditionType>active</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>armsDrop</effectType>
			<mul>1</mul>
			<duration>1</duration>
			<description>双倍黑色武器掉落</description>
		</skill>
		<skill name="equipDropDouble" cnName="双倍黑色装备掉落"><!-- 限制 -->
			<name>equipDropDouble</name>
			<cnName>双倍黑色装备掉落</cnName>
			<conditionType>active</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>equipDrop</effectType>
			<mul>1</mul>
			<duration>1</duration>
			<description>双倍黑色装备掉落</description>
		</skill>
		
		<skill name="armsDropDoubleAndGem" cnName="双倍黑色武器掉落和30%宝石掉落"><!-- 限制 -->
			<name>armsDropDoubleAndGem</name>
			<cnName>双倍黑色武器掉落</cnName>
			<conditionType>active</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>armsDropAndGem</effectType>
			<mul>1</mul>
			<value>0.3</value>
			<duration>1</duration>
			<description>双倍黑色武器掉落</description>
		</skill>
		<skill name="equipDropDoubleAndEquipGem" cnName="双倍黑色装备掉落和30%宝石掉落"><!-- 限制 -->
			<name>equipDropDoubleAndEquipGem</name>
			<cnName>双倍黑色装备掉落</cnName>
			<conditionType>active</conditionType>
			<target>me</target>
			<addType>state</addType>
			<effectType>equipDropAndGem</effectType>
			<mul>1</mul>
			<value>0.3</value>
			<duration>1</duration>
			<description>双倍黑色装备掉落</description>
		</skill>
		
		
		
		
		
		
		
		<skill name="highPetCard" cnName="神宠卡片"><!-- 限制 -->
			<name>highPetCard</name>
			<cnName>神宠卡片</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>highPetCard</effectType>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<description>4倍宠物伤害</description>
		</skill>
		<skill name="highVehicleCard" cnName="神车卡片"><!-- 限制 -->
			<name>highVehicleCard</name>
			<cnName>神车卡片</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>highVehicleCard</effectType>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<description>4倍载具伤害</description>
		</skill>
				<skill name="highCardState" cnName="神宠卡片、神车卡片状态"><!-- 限制 -->
					<name>highCardState</name>
					<cnName>4倍载具伤害</cnName>
					<everNoClearB>1</everNoClearB>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<target>me</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>otherHurtMul</effectType>
					<mul>4</mul>
					<duration>1</duration>
					<!--图像 ------------------------------------------------------------ -->
					<description>4倍伤害</description>
				</skill>
				
				
		<skill name="highPartnerCard" cnName="神队卡片"><!-- 限制 -->
			<name>highPartnerCard</name>
			<cnName>神队卡片</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>highPartnerCard</effectType>
			<duration>1</duration>
			<!--图像 ------------------------------------------------------------ -->
			<description>1倍持枪队友伤害</description>
		</skill>
				<skill name="highPartnerState" cnName="神队卡片状态"><!-- 限制 -->
					<name>highPartnerState</name>
					<cnName>1倍队友伤害</cnName>
					<everNoClearB>1</everNoClearB>
					<!--触发条件与目标------------------------------------------------------------ -->
					<conditionType>passive</conditionType>
					<target>me</target>
					<!--效果------------------------------------------------------------ -->
					<addType>state</addType>
					<effectType>otherHurtMul</effectType>
					<mul>2</mul>
					<duration>1</duration>
					<!--图像 ------------------------------------------------------------ -->
					<description>1倍伤害</description>
				</skill>
				
				
				
		<skill index="0" cnName="磁力场"><!-- 生存-主动 -->
			<name>electromagnet</name>
			<cnName>磁力场</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>magneticB</effectType>
			<duration>1</duration>
			<range>200</range>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<passiveSkillArr>magneticField_enemy_link</passiveSkillArr>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg  partType="mouth" con="add">skillEffect/magneticField</stateEffectImg>
			<description>向外释放磁力场，使700码的范围内的所有敌方子弹偏离轨道。</description>
		</skill>
		
		
		<skill index="0" cnName="超级散射卡"><!-- 生存-主动 -->
			<name>superSpreadCard</name>
			<cnName>超级散射卡</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>setBulletNum</effectType>
			<value>5</value><!-- bulletNumMul -->
			<mul>5</mul><!-- shootAngleMul -->
			<duration>1</duration>
		</skill>
		
		<skill index="0" cnName="骷髅卡"><!-- 生存-主动 -->
			<name>skeletonCard</name>
			<cnName>骷髅卡</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>1</duration>
			<passiveSkillArr>skeletonCard_link</passiveSkillArr>	
			<description></description>
		</skill>
					<skill>
						<name>skeletonCard_link</name>
						<cnName>骷髅卡扣血</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
						<!--触发条件与目标------------------------------------------------------------ -->
						<conditionType>active</conditionType>
						<condition>interval</condition>
						<target>me,range,enemy</target>
						<!--效果------------------------------------------------------------ -->
						<addType>state</addType>
						<effectType>skeletonCard</effectType>
						<mul>0.02</mul>
						<duration>1</duration>
						<range>99999</range>
						<!--图像------------------------------------------------------------ --> 
						<stateEffectImg  partType="body" con="add">generalEffect/bloodLoss</stateEffectImg>
						<description>使用后在关卡中（任务、修罗模式无效）让敌人每秒损失2%的生命值（首领每秒损失1%，生命低于15%时不再损失）。</description>
					</skill>
		
		
		
		
		
		
		
		
	</father>	
	<father name="fashion" cnName="时装技能">
		<skill cnName="南瓜头技能"><!-- dps -->
			<name>pumpkinHead</name>
			<cnName>后来居上</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>noDemonOnlyBoss,noTask,noUnionBattle</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>lessRedArms</effectType>
			<mul>10</mul>
			<description>红色及红色以下的武器，伤害提升10倍。主线、争霸、修罗决斗无效。</description>
		</skill>
		<![CDATA[
		<skill cnName="南瓜头技能"><!-- dps -->
			<name>pumpkinHead</name>
			<cnName>夜空隐</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>pumpkinHead</effectType>
			<duration>99999999</duration>
			<description>在任何地图中（非任务），只要离开地面就能隐身2秒，攻击不会打破隐身状态。</description>
		</skill>
		
		]]>
		<skill name="wolfFashionSkill" cnName=""><!-- dps -->
			<name>wolfFashionSkill</name>
			<cnName>狼震</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>skillCombo</condition>
			<otherConditionArr>skillCombo</otherConditionArr>
			<conditionRange>1</conditionRange>
			<conditionString>crazy_hero,feedback_hero</conditionString>
			<target limitNum="20">me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>normal_hurt</effectType>
			<extraValueType>nowArmsTrueDps</extraValueType>
			<mul>10</mul>
			<range>1000</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/wolfFashionSkill"></meEffectImg>
			<targetEffectImg con="add">bulletHitEffect/wolfShake</targetEffectImg>
			<description>同时触发人物技能狂暴、电离折射时，对周围1000码内的敌人发起狼震攻击，造成相当于当前武器面板伤害*射速*10的伤害。</description>
		</skill>
		
		
		<skill>
			<name>goldFalcon</name>
			<cnName>金翼</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instantAndState</addType>
			<effectType>goldFalcon</effectType>
			<description>进入关卡将长出黄金翅膀，让角色一直处于飞行状态。主线任务、首领工厂的困难模式下无效。未穿该时装时，可用“使用外观”来让角色获得该技能效果。可使用“I”键来切换陆空状态。</description>
			<duration>9999999</duration>
		</skill>
		
		<skill>
			<name>dragonHeadSkill</name>
			<cnName>龙翅</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType><summonedUnitsB>1</summonedUnitsB>
			<effectType>dragonHeadSkill</effectType>
			<obj>"cnName":"异火龙"</obj>
			<description>进入关卡将长出龙翅，让角色一直处于飞行状态。主线任务、争霸下无效。可使用“I”键来切换陆空状态。</description>
		</skill>
		<skill>
			<name>zombieMask</name>
			<cnName>伪装</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<target>me</target>
			<otherConditionArr>noTask</otherConditionArr>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>noAiFind</effectType>
			<description>伪装成僵尸，使敌人永远不会攻击你。任务中无效。</description>
			<duration>9999999</duration>
		</skill>
		
		<skill cnName="恶魔风脚"><!-- dps+限制 -->
			<name>crazy_sanji</name>
			<cnName>恶魔风脚</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>vehicleOver</condition><!-- 在准备攻击之前触发 -->
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>shootSpeedAndHurt</effectType>
			<mul>2</mul>
			<secMul>2</secMul>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg soundUrl="sound/crazy_hero"></meEffectImg>
			<stateEffectImg partType="2hand,2leg" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
			<description>载具使用完后，自身燃起烈火，攻击力增加100%，射击速度增加100%，不消耗子弹（非修罗模式），持续5秒。</description>
		</skill>
		<skill cnName="飞雷神"><!-- dps -->
			<name>xiaoBoShoot</name>
			<cnName>飞雷神</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>usewolongStick</condition>
			<target>me</target>
			<delay>0.15</delay>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>bullet</effectType>
			<!-- 子弹所需 -->
			<obj>"name":"xiaoBoShoot","site":"me","flipB":true</obj>
			<!--图像------------------------------------------------------------ -->
			<description>挥舞沃龙牌棍棒，释放出分身，对前方的敌人造成最大生命值12%的伤害。修罗模式、99级主线任务中效果降低。</description>
		</skill>
		<skill cnName="鸣人"><!-- dps -->
			<name>xiaoMingShoot</name>
			<cnName>疾风斩</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>usebutcherBlade</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bullet_xiaoMing</effectType>
			<mul>0.15</mul>
			<value>70</value>
			<duration>1.2</duration>
			<!-- 子弹所需 -->
			<obj>"name":"knife_xiaoMing"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>挥舞屠夫砍击前方所有敌人，对普通单位造成瞬秒伤害，对首领造成最大生命值15%的伤害。修罗模式、99级主线任务下效果降低。</description>
		</skill>
		<skill index="1" name="瞬秒-小怪">
			<name>seckillNormalEnemy</name>
			<cnName>瞬秒</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>seckillNormalEnemy</effectType>
		</skill>
		
		<skill cnName="小炎戒"><!-- dps -->
			<name>xiaoAiShoot</name>
			<cnName>小炎戒</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>poison_xiaoAi</effectType>
			<extraValueType>targetMaxLife</extraValueType>
			<mul>0.011</mul>
			<doGap>1</doGap>
			<duration>2</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="arm_right_1,arm_left_1,leg_right_1,leg_left_1" con="add" raNum="30">skillEffect/redFire</stateEffectImg>
			<description>将火焰灌输进子弹，高温子弹使敌人燃烧，每秒造成最大生命值1.1%的灼烧伤害。修罗模式下效果降低。</description>
		</skill>
		<skill cnName="英雄跳斩-降低目标攻击力"><!-- dps -->
			<name>shotgunBladeHero</name>
			<cnName>英雄跳斩-降低目标攻击力</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>hurtMul</effectType>
			<mul>0.3</mul>
			<duration>5</duration>
			<stateEffectImg partType="2hand,arm_right_0,arm_right_1,arm_left_0,arm_left_1">skillEffect/disabled_enemy</stateEffectImg>
			<description>减少被击中目标[1-mul]的攻击力。</description>
		</skill>
		
		<skill name="chinaCaptainSkill" cnName="队长之魄"><!-- dps -->
			<name>chinaCaptainSkill</name>
			<cnName>队长之魄</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>cdMul</effectType>
			<mul>1.3</mul>
			<duration>2</duration>
			<range>1500</range>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<description>震慑1500码内的敌人，使他们所有主动技能冷却时间延长30%。</description>
		</skill>
		<skill name="armyCommanderSkill" cnName="鼓舞士气"><!-- dps -->
			<name>armyCommanderSkill</name>
			<cnName>鼓舞士气</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target noMainB="1">me,range,we</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>underHurtMulAndMul</effectType>
			<mul>0.7</mul>
			<secMul>0.7</secMul>
			<duration>2</duration>
			<range>99999</range>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<stateEffectImg partType="mouth" con="add">skillEffect/rune_blue_shield</stateEffectImg>
			<description>使周围队友（除了P1角色）的防御力提升30%、百分比伤害降低30%。</description>
		</skill>
		<skill><!-- dps -->
			<name>snowShadowSkill</name>
			<cnName>怒目</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>snowShadowSkill</effectType>
			<value>66</value>
			<mul>1.5</mul>
			<duration>999999</duration>
			<description>保持移动速度不降低，受到伤害时能保持准星；初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用月刺将拥有较大攻击范围。</description>
		</skill>
		<skill name="foxLingSkill"><!-- dps -->
			<name>foxLingSkill</name>
			<cnName>流萤扇</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bladeSkill</effectType>
			<value>33</value>
			<mul>2.3</mul>
			<duration>999999</duration>
			<description>初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用剑齿镖时将投出跟踪流萤扇。</description>
		</skill>
		
		
		<skill name="bladeSkill"><!-- dps -->
			<name>bladeSkill</name>
			<cnName>盛怒</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>bladeSkill</effectType>
			<value>33</value>
			<mul>2.3</mul>
			<duration>999999</duration>
			<description>初始拥有[value]%的怒气，怒气回复速度增加至原来的[mul]；使用屠夫时将拥有超大攻击范围。</description>
		</skill>
		
		<skill name="hundredGhostsSkill"><!-- dps -->
			<name>hundredGhostsSkill</name>
			<cnName>鬼诀</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB><noBeClearB>1</noBeClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>999999</duration>
			<description>使用凯撒有专属动作；在装备8级镭射穿梭器时，还会在出招前瞬移到鼠标点位置（可在“系统>其他”中设置成双击瞬移）。</description>
		</skill>
		
		
		<skill name="cyanArmySkill" cnName="无敌之怒"><!-- dps -->
			<name>cyanArmySkill</name>
			<cnName>无敌之怒</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<otherConditionArr>hurtUnderIsInvincible</otherConditionArr>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>otherHurtMul</effectType>
			<mul>4</mul>
			<duration>3</duration>
			<!--技能链接或者需要添加的被动技能------------------------------------------------------------ --> 
			<stateEffectImg partType="shootPoint" con="filter" raNum="30">bulletHitEffect/fireSmoke</stateEffectImg>
			<description>攻击无敌怪物时，你的伤害会提高400%，持续3秒。</description>
		</skill>
		
		
		
		
		<skill index="0" name="生化锁定"><!-- dps-被动 -->
			<name>bioShockSkill</name>
			<cnName>生化锁定</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>boss</valueString>
			<mul>1.30</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对首领造成额外30%的伤害。</description>
		</skill>
		<skill><!-- dps-被动 -->
			<name>hurtBossAdd</name>
			<cnName>生化锁定X</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>changeHurt</effectType>
			<valueString>boss</valueString>
			<mul>2</mul>
			<!-- 修改伤害所需 -->
			<obj>"type":"enemyType"</obj>
			<!--图像------------------------------------------------------------ -->
			<description>对首领造成额外[mul-1]的伤害。</description>
		</skill>
	</father>	
	
	<father name="vehicleNormal" cnName="载具技能">	
		<skill cnName="挖掘者-动能推撞"><!-- dps -->
			<name>highDrill</name>
			<cnName>动能推撞</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeedAllStop</effectType>
			<mul>0</mul>
			<duration>1</duration>
			<!--图像------------------------------------------------------------ --> 
			<description></description>
		</skill>
		<skill cnName="挖掘者-超能推撞"><!-- dps -->
			<name>superHighDrill</name>
			<cnName>超能推撞</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeedAllStop</effectType>
			<mul>0</mul>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ --> 
			<stateEffectImg partType="arm_right_0,arm_right_1,arm_left_0,arm_left_1,leg_right_0,leg_right_1,leg_left_0,leg_left_1" con="filter" raNum="30">skillEffect/redFire</stateEffectImg>
			<description></description>
		</skill>
		<skill cnName="挖掘者-动能推撞-推">
			<name>highDrillHit</name>
			<cnName>挖掘者-动能推撞-推</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>highDrillHit</effectType>
			<!--图像------------------------------------------------------------ --> 
			<description></description>
		</skill>	
		
		
	</father>
	
	<father name="forest" cnName=""><![CDATA[其他==================================================================================]]>
		<skill>
			<name>unendBuff</name>
			<cnName>虚天塔Buff</cnName><ignoreNoSkillB>1</ignoreNoSkillB><ignoreSilenceB>1</ignoreSilenceB><everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>unendBuff</effectType>
			<duration>9999999999</duration>
		</skill>
		
		<skill>
			<name>addChargerMax2</name>
			<cnName>2倍携弹量</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>addChargerMaxMul</effectType>
			<mul>2</mul>
		</skill>
		<skill>
			<name>extendCd</name>
			<cnName>技能衰减</cnName>
			<showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><ignoreImmunityB>1</ignoreImmunityB>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>cdMulAndMinTrigger</effectType>
			<mul>1.5</mul>
			<duration>2</duration>
			<range>99999</range>
			<description>使周围所有敌人的技能冷却时间延长1倍。</description>
		</skill>
		<skill name="竞技场-伤害加倍">
			<name>arenaHurtAdd</name>
			<cnName>竞技场-伤害加倍</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType><overlyingB>1</overlyingB>
			<effectType>arenaHurtAdd</effectType>
			<mul>2</mul>
			<duration>9999999</duration>
		</skill>
		
		<skill index="0" name="击中减速">
			<name>Hit_SlowMove</name>
			<cnName>击中减速</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.5</mul>
			<duration>2</duration>
			<range>0</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
		</skill>
		
		<skill index="0" name="减速光环">
			<name>Range_SlowMove</name>
			<cnName>减速光环</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>interval</condition>
			<target>me,range,enemy</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>0</value>
			<mul>0.3</mul>
			<duration>1</duration>
			<range>300</range>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
			
		</skill>
		
		<skill index="0" name="主动加血">
			<name>Manual_AddLife</name>
			<cnName>主动加血</cnName>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>life</effectType>
			<value>10</value>
		</skill>
		<skill index="0" name="反弹伤害">
			<name>BackHurt</name>
			<cnName>反弹伤害</cnName>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>underHit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>backHurt</effectType>
			<extraValueType>hurtValue</extraValueType>
			<value>0</value>
			<mul>0.2</mul>
			<duration>3</duration>
			<!--图像------------------------------------------------------------ -->
			<meEffectImg></meEffectImg>
			<targetEffectImg></targetEffectImg>
		</skill>
		
		
		<skill>
			<name>pumpkinDropEffect</name>
			<cnName>怪物带着南关头</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>99999</duration>
			<stateEffectImg partType="head">pumpkinHead/effect</stateEffectImg>
		</skill>
		<skill>
			<name>headOrangeLight</name>
			<cnName>怪物带着南关头</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<duration>99999</duration>
			<stateEffectImg name="headOrangeLight"/>
		</skill>
		
		<![CDATA[gm面具==================================================================================]]>
		<skill index="0" name="火花面具">
			<name>gmMask1</name>
			<cnName>花火面具</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>gmMask</effectType>
			<valueString>AnniverUI/mask1</valueString>
			<secString>花火</secString>
			<duration>9999999</duration>
			<stateEffectImg partType="mouth">AnniverUI/huahuo</stateEffectImg>
			<description>带上面具，你就是花火！</description>
		</skill>
		<skill index="0" name="沃龙面具">
			<name>gmMask2</name>
			<cnName>沃龙面具</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>gmMask</effectType>
			<valueString>AnniverUI/mask2</valueString>
			<secString>沃龙</secString>
			<duration>9999999</duration>
			<stateEffectImg partType="mouth">AnniverUI/wolong</stateEffectImg>
			<description>带上面具，你就是沃龙！</description>
		</skill>
		<skill index="0" name="丛安">
			<name>gmMask3</name>
			<cnName>丛安面具</cnName>
			<everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>gmMask</effectType>
			<valueString>AnniverUI/mask3</valueString>
			<secString>丛安</secString>
			<duration>9999999</duration>
			<stateEffectImg partType="mouth">AnniverUI/congan</stateEffectImg>
			<description>带上面具，你就是丛安！</description>
		</skill>
	</father>

	<![CDATA[任务==================================================================================]]>
	<father name="task" cnName="">
		<skill cnName="翻滚"><!-- dps -->
			<name>rollingFast</name>
			<cnName>翻滚</cnName>
			<iconUrl>SkillIcon/rolling_hero</iconUrl>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>moveCombo</condition>
			<otherConditionArr>moveCombo</otherConditionArr>
			<conditionString>left,left|right,right</conditionString>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>heroRolling</effectType>
			<minTriggerT>1</minTriggerT>
			<mul>0</mul>
			<value>500</value>
			<valueString></valueString>
			<duration>0.5</duration>
			<!-- 子弹所需 -->
			<stateEffectImg xGap="30" con="filter" raNum="1">skillEffect/rollingShadow</stateEffectImg>
			<!--图像------------------------------------------------------------ -->
			<description>双击移动按键可向前翻滚[value]码，期间不受攻击、子弹碰撞，最短触发间隔[minTriggerT]秒。</description>
		</skill>
		<skill>
			<name>speedUpTask</name>
			<cnName>加速</cnName><iconUrl36>SkillIcon/groupSpeedUp_enemy_36</iconUrl36>
			<!--英雄技能属性------------------------------------------------------------ -->
			<cd>20</cd>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>active</conditionType>
			<condition>avtiveSkillCdOver</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>moveSpeed</effectType>
			<value>10</value>
			<duration>5</duration>
			<!--图像------------------------------------------------------------ --> 
			<meEffectImg soundUrl="sound/groupSpeedUp_enemy"></meEffectImg>
			<stateEffectImg partType="2foot" con="add" raNum="30">bulletHitEffect/smoke_small</stateEffectImg>
			<description>技能释放后，提升自身移动速度。</description>
		</skill>
		
		
		<skill name="超级散射">
			<name>superMoreBullet</name>
			<cnName>超级散射</cnName><everNoClearB>1</everNoClearB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>state</addType>
			<effectType>setBulletNum</effectType>
			<value>5</value><!-- bulletNumMul -->
			<mul>5</mul><!-- shootAngleMul -->
			<valueString>week</valueString><!-- 每周每种武器只能附加一次这种效果（先把武器列表记录在关卡内，任务完成之后在level.xml中才把武器列表加入存档） -->
			<duration>999999</duration>
			<!--图像------------------------------------------------------------ -->
			<description>增加[value]倍散射子弹。</description>
		</skill>
		<skill name="无限馈赠">
			<name>superSkillGift</name>
			<cnName>无限馈赠</cnName><everNoClearB>1</everNoClearB>
			<conditionType>passive</conditionType>
			<condition>useSkill</condition>
			<target>me</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>heroSkillFullCd</effectType>
			<value>2</value>
		</skill>
		
		<skill name="极限射速">
			<name>maxSpeedTask</name>
			<cnName>极限射速</cnName><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<conditionType>passive</conditionType>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>maxSpeedTask</effectType>
			<mul>5</mul><!-- shootAngleMul -->
			<duration>99999</duration>
			<description>增加[mul]倍的射击速度。</description>
			<stateEffectImg partType="2eye,2hand" con="filter" raNum="30">skillEffect/smallFire</stateEffectImg>
		</skill>
		
		<skill name="战争狂人脱下头盔">
			<name>madmanHead</name>
			<cnName>战争狂人脱下头盔</cnName>
			<conditionType>active</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>no</condition>
			<target>me</target>
			<addType>instant</addType>
			<effectType>madmanHead</effectType>
			<meEffectImg soundUrl="uiSound/swapHero"  con="add" partType="head">generalEffect/blackHoleHide</meEffectImg>
			<targetEffectImg  partType="head">generalEffect/blackHoleHide</targetEffectImg>
		</skill>
		<skill name="丛林特种兵技能">
			<name>betHit</name>
			<cnName>丛林特种兵技能</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>hit</condition>
			<target>me</target>
			<addType>instant</addType>
			<effectType>betHit</effectType>
			
		</skill>
		<skill name="瞬秒所有">
			<name>killAll</name>
			<cnName>瞬秒</cnName><noSkillDodgeB>1</noSkillDodgeB><ignoreNoSkillB>1</ignoreNoSkillB>
			<!--触发条件与目标------------------------------------------------------------ -->
			<conditionType>passive</conditionType>
			<condition>hit</condition>
			<target>target</target>
			<!--效果------------------------------------------------------------ -->
			<addType>instant</addType>
			<effectType>kill</effectType>
		</skill>
		<skill>
			<name>attackNoDodge</name>
			<cnName>攻击无视闪避</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>attackNoDodge</effectType>
			<duration>99999</duration>
		</skill>
		<skill>
			<name>findHide</name>
			<cnName>红外眼</cnName><showInLifeBarB>1</showInLifeBarB><wantDescripB>1</wantDescripB>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>findHideB</effectType>
			<duration>999999</duration>
			<description>可看见隐身敌人。</description>
		</skill>
		
		<skill>
			<name>invincibleZombieEnemyHit</name>
			<cnName>无敌自爆僵尸攻击P1角色</cnName>
			<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<condition>hit</condition>
			<target>me</target>
			<addType>instant</addType>
			<effectType>onlyHurtMul</effectType>
			<mul>0.55</mul>
		</skill>
		
		
		<skill>
			<name>sniperKingBuff</name>
			<cnName>狙击之王任务P1角色状态</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>sniperKingBuff</effectType>
			<duration>99999</duration>
		</skill>
						<skill>
							<name>sniperKingEnemyHit</name>
							<cnName>狙击之王怪物攻击P1角色</cnName>
							<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
							<condition>hit</condition>
							<target>me</target>
							<addType>instant</addType>
							<effectType>sniperKingEnemyHit</effectType>
							<pointEffectImg>skillEffect/paralysis_enemy</pointEffectImg>
						</skill>
						
						<skill>
							<name>sniperKingEnemyUnder</name>
							<cnName>狙击之王怪物受攻击</cnName>
							<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
							<condition>underHit</condition>
							<target>me</target>
							<addType>instant</addType>
							<effectType>sniperKingEnemyUnder</effectType>
						</skill>
						<skill>
							<name>sniperKingEnemyUnder2</name>
							<cnName>狙击之王怪物受攻击-技能</cnName>
							<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
							<condition>underSkillHit</condition>
							<target>me</target>
							<addType>instant</addType>
							<effectType>sniperKingEnemyUnder</effectType>
						</skill>
						
		<skill>
			<name>bulletRainBallHit</name>
			<cnName>子弹碰撞</cnName>
			<conditionType>passive</conditionType><noSkillDodgeB>1</noSkillDodgeB><ignoreImmunityB>1</ignoreImmunityB>
			<condition>hit</condition>
			<target>me</target>
			<addType>instant</addType>
			<effectType>bulletRainBallHit</effectType>
		</skill>
		<skill>
			<name>flySkyBatBuff</name>
			<cnName>蝙蝠状态</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>add</condition>
			<target>me</target>
			<addType>state</addType>
			<effectType>no</effectType>
			<stateEffectImg con="add" partType="body">generalEffect/nothing</stateEffectImg>
			<pointEffectImg soundUrl="sound/body_hit">boomEffect/midWood</pointEffectImg>
			<duration>99999</duration>
		</skill>
		
		
		<![CDATA[某个武器发射多少次之后，完成指定任务
		<skill>
			<name>newYearBlessing</name>
			<cnName>祝福语任务完成</cnName>
			<conditionType>passive</conditionType><noBeClearB>1</noBeClearB><everNoClearB>1</everNoClearB>
			<condition>heroShoot</condition>
			<conditionString>highlighter</conditionString>
			<target>me</target>
			<addType>instant</addType>
			<effectType>newYearBlessing</effectType><effectFather>task</effectFather>
			<value></value>
			<valueString></valueString>
		</skill>
		]]>
	</father>
</data>